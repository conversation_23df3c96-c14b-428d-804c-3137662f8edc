# 🚀 AP3X Crypto Agent

## Overview
Advanced crypto analysis agent with **self-healing capabilities**, **114 tools**, and **sophisticated multi-step reasoning**.

## Quick Start

### Interactive Mode
```bash
python cli.py
```

### Single Query
```bash
python cli.py --query "Search for Bitcoin news and analyze wallet 0x742d35Cc..."
```

### Check System Health
```bash
python cli.py --health
```

## Features
- **🧠 Multi-Step Reasoning**: Sophisticated planning and execution
- **🏥 Self-Healing**: Automatic failure detection and recovery
- **🛠️ 114 Tools**: Math, search, blockchain analysis, strategic thinking
- **🔄 Circuit Breakers**: Prevents cascade failures
- **📊 Health Monitoring**: Real-time system status
- **🎯 Adaptive Fallbacks**: Performance-based service selection

## Available Tools
- **Utility**: Math operations, text processing, crypto utilities (14 tools)
- **Web Search**: Tavily + DuckDuckGo with intelligent fallback (6 tools)
- **Blockchain**: Comprehensive Moralis API integration (93 tools)
- **Reasoning**: Sequential thinking for strategic analysis (1 tool)

## CLI Commands
- `health` - System health status
- `check` - Force health check
- `recover <component>` - Manual recovery
- `tools` - List available tools
- `help` - Show help
- `quit` - Exit

## Configuration
- API keys in `.env` file
- MCP servers in `mcp_config.py`
- Self-healing auto-configured

## Architecture
- **agent.py**: Core agent with self-healing
- **cli.py**: Terminal interface
- **enhanced_agent.py**: Multi-step reasoning engine
- **self_healing.py**: Health monitoring and recovery
- **mcp_config.py**: MCP server configuration
- **simple_mcp_server.py**: Local utility server

## Example Queries
- "Search for Bitcoin news and provide investment advice"
- "Analyze wallet 0x742d35Cc... and suggest optimizations"
- "What's the current price of Ethereum and should I buy?"
- "Get NFTs for wallet abc123 and calculate portfolio value"

Your **enterprise-grade crypto AI agent** is ready! 🎉