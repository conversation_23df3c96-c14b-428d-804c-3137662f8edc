#!/usr/bin/env python3
"""
Safe Filesystem Cleanup Plan for AP3X Crypto Agent
Removes unnecessary files while preserving all advanced self-healing agent functionality.
"""

import os
import shutil
import sys
from pathlib import Path
from typing import List, Dict, Set

class Colors:
    """ANSI color codes for terminal output."""
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def analyze_dependencies() -> Dict[str, Set[str]]:
    """Analyze file dependencies to understand what's needed."""
    
    # Core files that are essential for the advanced self-healing agent
    essential_files = {
        # Self-healing core system
        'self_healing.py': {'psutil', 'aiohttp'},
        'enhanced_agent_healing.py': {'self_healing.py', 'enhanced_agent.py', 'mcp_config.py'},
        'self_healing_cli.py': {'enhanced_agent_healing.py'},
        
        # Enhanced agent core (dependency of self-healing)
        'enhanced_agent.py': {'mcp_config.py'},
        
        # Configuration and utilities
        'mcp_config.py': {},
        'simple_mcp_server.py': {},  # Required by mcp_config.py
        
        # Environment and requirements
        'requirements.txt': {},
        '.env': {},  # If it exists
        
        # Essential documentation
        'SELF_HEALING_ARCHITECTURE.md': {},
        'README.md': {},
        
        # Launcher scripts
        'run_agent.bat': {},
        'run_agent.sh': {},
    }
    
    # Files that can be safely removed
    removable_files = {
        # Legacy/basic agents (superseded by self-healing agent)
        'agent.py': 'Legacy basic agent - superseded by self-healing enhanced agent',
        'cli.py': 'Legacy basic CLI - superseded by self_healing_cli.py',
        'chat.py': 'Legacy chat interface - functionality in self_healing_cli.py',
        
        # Testing files (useful for development but not needed for production)
        'test_enhanced_agent.py': 'Testing file - not needed for production use',
        'test_final_enhanced_agent.py': 'Testing file - not needed for production use',
        'test_search_engines.py': 'Testing file - not needed for production use',
        'test_self_healing.py': 'Testing file - not needed for production use',
        
        # Example/demo files
        'example.py': 'Example/demo file - not needed for production',
        'advanced_examples.py': 'Example/demo file - not needed for production',
        
        # Integration files (one-time use)
        'integrate_self_healing.py': 'One-time integration script - no longer needed',
        
        # Redundant CLI files
        'enhanced_cli.py': 'Superseded by self_healing_cli.py with backward compatibility',
        'enhanced_cli_integrated.py': 'Redundant - self_healing_cli.py provides same functionality',
        
        # Redundant documentation
        'ENHANCED_AGENT_README.md': 'Superseded by SELF_HEALING_ARCHITECTURE.md',
        'TRANSFORMATION_SUMMARY.md': 'Historical document - no longer needed',
        'QUICKSTART.md': 'Superseded by SELF_HEALING_ARCHITECTURE.md',
        
        # Cache/temporary files
        '__pycache__': 'Python cache directory - can be regenerated',
    }
    
    # Files to keep but could be archived
    archivable_files = {
        'backup_original': 'Original file backups - could be archived',
    }
    
    return {
        'essential': essential_files,
        'removable': removable_files,
        'archivable': archivable_files
    }

def create_archive_directory():
    """Create archive directory for files we want to keep but not clutter the main directory."""
    archive_dir = Path("archive")
    archive_dir.mkdir(exist_ok=True)
    return archive_dir

def backup_before_cleanup():
    """Create a safety backup before cleanup."""
    backup_dir = Path("pre_cleanup_backup")
    if backup_dir.exists():
        shutil.rmtree(backup_dir)
    
    backup_dir.mkdir()
    
    # Backup all Python files and important configs
    important_patterns = ['*.py', '*.md', '*.txt', '*.bat', '*.sh']
    
    for pattern in important_patterns:
        for file_path in Path('.').glob(pattern):
            if file_path.is_file():
                shutil.copy2(file_path, backup_dir / file_path.name)
    
    print(f"{Colors.GREEN}✅ Safety backup created in {backup_dir}/{Colors.END}")
    return backup_dir

def verify_essential_files_exist(essential_files: Dict[str, Set[str]]) -> bool:
    """Verify all essential files exist before cleanup."""
    missing_files = []
    
    for file_name in essential_files.keys():
        if not Path(file_name).exists():
            missing_files.append(file_name)
    
    if missing_files:
        print(f"{Colors.RED}❌ Missing essential files: {missing_files}{Colors.END}")
        return False
    
    print(f"{Colors.GREEN}✅ All essential files present{Colors.END}")
    return True

def safe_remove_file(file_path: Path, reason: str) -> bool:
    """Safely remove a file with logging."""
    try:
        if file_path.is_file():
            file_path.unlink()
            print(f"  {Colors.YELLOW}🗑️  Removed: {file_path.name} - {reason}{Colors.END}")
            return True
        elif file_path.is_dir():
            shutil.rmtree(file_path)
            print(f"  {Colors.YELLOW}🗑️  Removed directory: {file_path.name} - {reason}{Colors.END}")
            return True
        else:
            print(f"  {Colors.BLUE}ℹ️  Not found: {file_path.name}{Colors.END}")
            return False
    except Exception as e:
        print(f"  {Colors.RED}❌ Failed to remove {file_path.name}: {e}{Colors.END}")
        return False

def archive_file(file_path: Path, archive_dir: Path, reason: str) -> bool:
    """Archive a file instead of deleting it."""
    try:
        if file_path.exists():
            if file_path.is_dir():
                shutil.copytree(file_path, archive_dir / file_path.name, dirs_exist_ok=True)
                shutil.rmtree(file_path)
            else:
                shutil.copy2(file_path, archive_dir / file_path.name)
                file_path.unlink()
            print(f"  {Colors.CYAN}📦 Archived: {file_path.name} - {reason}{Colors.END}")
            return True
        else:
            print(f"  {Colors.BLUE}ℹ️  Not found: {file_path.name}{Colors.END}")
            return False
    except Exception as e:
        print(f"  {Colors.RED}❌ Failed to archive {file_path.name}: {e}{Colors.END}")
        return False

def create_streamlined_readme():
    """Create a streamlined README for the cleaned up system."""
    
    readme_content = """# 🚀 AP3X Crypto Agent - Self-Healing Advanced AI System

## 🎯 Overview
This is your **advanced self-healing crypto agent** with sophisticated multi-step reasoning, automatic failure recovery, and comprehensive blockchain analysis capabilities.

## 🚀 Quick Start

### Start the Self-Healing Agent
```bash
python self_healing_cli.py
```

### Check System Health
```bash
python self_healing_cli.py --health
```

### Single Query Mode
```bash
python self_healing_cli.py --query "Search for Bitcoin news and analyze wallet 0x742d35Cc..."
```

## 🏥 Self-Healing Features
- **Automatic Failure Detection**: Real-time monitoring of all components
- **Intelligent Recovery**: Multi-strategy automatic recovery mechanisms
- **Circuit Breaker Protection**: Prevents cascade failures
- **Adaptive Fallbacks**: Performance-based service selection
- **Health Monitoring**: Comprehensive system health reporting

## 🛠️ Available Tools (114 Total)
- **Utility Tools**: Math, text processing, crypto utilities
- **Web Search**: Tavily + DuckDuckGo with intelligent fallback
- **Blockchain Analysis**: Comprehensive Moralis API integration (93 tools)
- **Advanced Reasoning**: Sequential thinking for strategic analysis

## 💬 CLI Commands
- `health` - Show system health status
- `check` - Force health check
- `recover <component>` - Manual recovery trigger
- `help` - Show detailed help
- `tools` - List available tools

## 🔧 Configuration
- Uses existing `.env` file for API keys
- MCP server configuration in `mcp_config.py`
- Self-healing settings auto-configured

## 📊 System Architecture
See `SELF_HEALING_ARCHITECTURE.md` for complete technical documentation.

## 🎯 Key Benefits
✅ **99%+ Reliability** - Automatic failure recovery
✅ **Seamless Experience** - Transparent error handling
✅ **Performance Optimization** - Adaptive service selection
✅ **Comprehensive Analysis** - 114 tools across 5 MCP servers
✅ **Strategic Intelligence** - Multi-step reasoning with synthesis

Your agent is now a **self-healing, enterprise-grade AI system**!
"""
    
    with open("README.md", "w") as f:
        f.write(readme_content)
    
    print(f"{Colors.GREEN}✅ Created streamlined README.md{Colors.END}")

def perform_cleanup():
    """Perform the actual cleanup operation."""
    
    print(f"{Colors.CYAN}{Colors.BOLD}")
    print("🧹 AP3X Crypto Agent - Filesystem Cleanup")
    print("=" * 50)
    print(f"{Colors.END}")
    
    # Step 1: Analyze dependencies
    print(f"\n{Colors.PURPLE}📋 Step 1: Analyzing Dependencies{Colors.END}")
    dependencies = analyze_dependencies()
    
    essential_files = dependencies['essential']
    removable_files = dependencies['removable']
    archivable_files = dependencies['archivable']
    
    print(f"  Essential files: {len(essential_files)}")
    print(f"  Removable files: {len(removable_files)}")
    print(f"  Archivable files: {len(archivable_files)}")
    
    # Step 2: Verify essential files exist
    print(f"\n{Colors.PURPLE}📋 Step 2: Verifying Essential Files{Colors.END}")
    if not verify_essential_files_exist(essential_files):
        print(f"{Colors.RED}❌ Cannot proceed - missing essential files{Colors.END}")
        return False
    
    # Step 3: Create safety backup
    print(f"\n{Colors.PURPLE}📋 Step 3: Creating Safety Backup{Colors.END}")
    backup_dir = backup_before_cleanup()
    
    # Step 4: Create archive directory
    print(f"\n{Colors.PURPLE}📋 Step 4: Creating Archive Directory{Colors.END}")
    archive_dir = create_archive_directory()
    
    # Step 5: Archive files
    print(f"\n{Colors.PURPLE}📋 Step 5: Archiving Files{Colors.END}")
    archived_count = 0
    for file_name, reason in archivable_files.items():
        if archive_file(Path(file_name), archive_dir, reason):
            archived_count += 1
    
    # Step 6: Remove unnecessary files
    print(f"\n{Colors.PURPLE}📋 Step 6: Removing Unnecessary Files{Colors.END}")
    removed_count = 0
    for file_name, reason in removable_files.items():
        if safe_remove_file(Path(file_name), reason):
            removed_count += 1
    
    # Step 7: Create streamlined documentation
    print(f"\n{Colors.PURPLE}📋 Step 7: Creating Streamlined Documentation{Colors.END}")
    create_streamlined_readme()
    
    # Step 8: Summary
    print(f"\n{Colors.GREEN}{Colors.BOLD}✅ Cleanup Complete!{Colors.END}")
    print(f"\n📊 Summary:")
    print(f"  {Colors.GREEN}Essential files preserved: {len(essential_files)}{Colors.END}")
    print(f"  {Colors.CYAN}Files archived: {archived_count}{Colors.END}")
    print(f"  {Colors.YELLOW}Files removed: {removed_count}{Colors.END}")
    print(f"  {Colors.BLUE}Safety backup: {backup_dir}/{Colors.END}")
    
    print(f"\n🎯 Your advanced self-healing agent is ready:")
    print(f"  {Colors.GREEN}python self_healing_cli.py{Colors.END}")
    
    return True

def show_cleanup_plan():
    """Show what will be cleaned up without actually doing it."""
    
    print(f"{Colors.CYAN}{Colors.BOLD}")
    print("🔍 AP3X Crypto Agent - Cleanup Plan Preview")
    print("=" * 50)
    print(f"{Colors.END}")
    
    dependencies = analyze_dependencies()
    
    print(f"\n{Colors.GREEN}✅ Files to KEEP (Essential):{Colors.END}")
    for file_name in dependencies['essential'].keys():
        status = "✅" if Path(file_name).exists() else "❌"
        print(f"  {status} {file_name}")
    
    print(f"\n{Colors.CYAN}📦 Files to ARCHIVE:{Colors.END}")
    for file_name, reason in dependencies['archivable'].items():
        status = "📁" if Path(file_name).exists() else "❌"
        print(f"  {status} {file_name} - {reason}")
    
    print(f"\n{Colors.YELLOW}🗑️  Files to REMOVE:{Colors.END}")
    for file_name, reason in dependencies['removable'].items():
        status = "🗑️" if Path(file_name).exists() else "❌"
        print(f"  {status} {file_name} - {reason}")
    
    print(f"\n{Colors.PURPLE}📋 Summary:{Colors.END}")
    print(f"  Essential files: {len(dependencies['essential'])}")
    print(f"  Files to archive: {len(dependencies['archivable'])}")
    print(f"  Files to remove: {len(dependencies['removable'])}")
    
    print(f"\n{Colors.BLUE}ℹ️  Run with --execute to perform the cleanup{Colors.END}")

def main():
    """Main cleanup script."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Safe filesystem cleanup for AP3X Crypto Agent")
    parser.add_argument("--execute", action="store_true", help="Actually perform the cleanup")
    parser.add_argument("--preview", action="store_true", help="Show cleanup plan without executing")
    
    args = parser.parse_args()
    
    if args.execute:
        success = perform_cleanup()
        sys.exit(0 if success else 1)
    else:
        show_cleanup_plan()
        sys.exit(0)

if __name__ == "__main__":
    main()
