#!/usr/bin/env python3
"""
Simple MCP Server with basic utility tools.
This server provides math operations, text processing, and crypto-related utilities.
"""

from mcp.server.fastmcp import FastMCP
import json
import hashlib
import base64
from typing import List, Dict, Any

# Initialize the MCP server
mcp = FastMCP("UtilityServer")

@mcp.tool()
def add(a: float, b: float) -> float:
    """Add two numbers together."""
    return a + b

@mcp.tool()
def multiply(a: float, b: float) -> float:
    """Multiply two numbers together."""
    return a * b

@mcp.tool()
def divide(a: float, b: float) -> float:
    """Divide first number by second number."""
    if b == 0:
        raise ValueError("Cannot divide by zero")
    return a / b

@mcp.tool()
def calculate_percentage(value: float, total: float) -> float:
    """Calculate what percentage value is of total."""
    if total == 0:
        raise ValueError("Total cannot be zero")
    return (value / total) * 100

@mcp.tool()
def text_length(text: str) -> int:
    """Get the length of a text string."""
    return len(text)

@mcp.tool()
def text_uppercase(text: str) -> str:
    """Convert text to uppercase."""
    return text.upper()

@mcp.tool()
def text_lowercase(text: str) -> str:
    """Convert text to lowercase."""
    return text.lower()

@mcp.tool()
def hash_text(text: str, algorithm: str = "sha256") -> str:
    """Hash text using specified algorithm (sha256, md5, sha1)."""
    text_bytes = text.encode('utf-8')
    
    if algorithm.lower() == "sha256":
        return hashlib.sha256(text_bytes).hexdigest()
    elif algorithm.lower() == "md5":
        return hashlib.md5(text_bytes).hexdigest()
    elif algorithm.lower() == "sha1":
        return hashlib.sha1(text_bytes).hexdigest()
    else:
        raise ValueError(f"Unsupported algorithm: {algorithm}")

@mcp.tool()
def encode_base64(text: str) -> str:
    """Encode text to base64."""
    return base64.b64encode(text.encode('utf-8')).decode('utf-8')

@mcp.tool()
def decode_base64(encoded_text: str) -> str:
    """Decode base64 text."""
    try:
        return base64.b64decode(encoded_text).decode('utf-8')
    except Exception as e:
        raise ValueError(f"Invalid base64 string: {e}")

@mcp.tool()
def format_json(json_string: str) -> str:
    """Format and prettify a JSON string."""
    try:
        parsed = json.loads(json_string)
        return json.dumps(parsed, indent=2, sort_keys=True)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON: {e}")

@mcp.tool()
def validate_json(json_string: str) -> Dict[str, Any]:
    """Validate if a string is valid JSON and return validation result."""
    try:
        parsed = json.loads(json_string)
        return {
            "valid": True,
            "message": "Valid JSON",
            "parsed_data": parsed
        }
    except json.JSONDecodeError as e:
        return {
            "valid": False,
            "message": f"Invalid JSON: {e}",
            "parsed_data": None
        }

@mcp.tool()
def crypto_address_info(address: str) -> Dict[str, Any]:
    """Get basic information about a cryptocurrency address."""
    # Basic validation and info extraction
    address = address.strip()
    
    info = {
        "address": address,
        "length": len(address),
        "type": "unknown"
    }
    
    # Basic address type detection
    if address.startswith("0x") and len(address) == 42:
        info["type"] = "ethereum"
        info["network"] = "ethereum"
    elif address.startswith("bc1") or address.startswith("1") or address.startswith("3"):
        info["type"] = "bitcoin"
        info["network"] = "bitcoin"
    elif len(address) == 44 and not address.startswith("0x"):
        info["type"] = "solana"
        info["network"] = "solana"
    
    return info

@mcp.tool()
def list_available_tools() -> List[str]:
    """List all available tools in this MCP server."""
    return [
        "add", "multiply", "divide", "calculate_percentage",
        "text_length", "text_uppercase", "text_lowercase",
        "hash_text", "encode_base64", "decode_base64",
        "format_json", "validate_json", "crypto_address_info",
        "list_available_tools"
    ]

if __name__ == "__main__":
    # Run the server with stdio transport
    mcp.run(transport="stdio")
