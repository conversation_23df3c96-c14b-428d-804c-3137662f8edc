#!/usr/bin/env python3
"""
Quick test to verify the address analysis fix.
"""

import asyncio
import sys
from enhanced_agent import create_enhanced_agent
from langchain_core.runnables import RunnableConfig

async def test_address_analysis():
    """Test the fixed address analysis functionality."""
    print("🧪 Testing Address Analysis Fix")
    print("=" * 40)
    
    try:
        # Initialize agent
        print("Initializing enhanced agent...")
        agent = await create_enhanced_agent()
        print("✅ Agent initialized successfully")
        
        # Test Solana address
        solana_address = "EUxS8Kqvbe8zDr8bG1RYtgjVNzEhMTG6B6H3WwrCHfwA"
        print(f"\nTesting Solana address: {solana_address}")
        
        config = RunnableConfig(recursion_limit=50, thread_id="test_address")
        
        # Test with balanced profile
        result = await agent.invoke(solana_address, execution_profile="balanced", config=config)
        
        print(f"\n✅ Test completed successfully!")
        print(f"Intent: {result.get('intent', 'unknown')}")
        print(f"Next Action: {result.get('next_action', 'unknown')}")
        print(f"Response Length: {len(result.get('final_response', ''))}")
        
        # Check if we got a proper response
        response = result.get('final_response', '')
        if 'failed' in response.lower() or 'error' in response.lower():
            print(f"⚠️  Response indicates an issue: {response[:200]}...")
        else:
            print(f"✅ Response looks good: {response[:200]}...")
        
        await agent.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the test."""
    success = await test_address_analysis()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
