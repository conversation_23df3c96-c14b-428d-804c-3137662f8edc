#!/usr/bin/env python3
"""
CLI interface for LangGraph MCP Agent
Run the agent from command line with various options.
"""

import asyncio
import argparse
import sys
import os
from typing import Optional
from dotenv import load_dotenv

from enhanced_agent import create_enhanced_agent, EnhancedLangGraphMCPAgent
from agent import create_agent, LangGraphMCPAgent
from langchain_core.runnables import RunnableConfig

# Load environment variables
load_dotenv()

class Colors:
    """ANSI color codes for terminal output."""
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_banner():
    """Print the CLI banner."""
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                    LangGraph MCP Agent                      ║
║                  Command Line Interface                     ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
"""
    print(banner)

def print_tools(agent):
    """Print available tools."""
    tools = agent.list_tools()
    print(f"\n{Colors.GREEN}📋 Available Tools ({len(tools)}):{Colors.END}")
    for i, tool in enumerate(tools, 1):
        print(f"  {Colors.YELLOW}{i:2d}.{Colors.END} {tool}")

async def interactive_mode(agent):
    """Run the agent in interactive chat mode."""
    print(f"\n{Colors.GREEN}💬 Enhanced Interactive Chat Mode{Colors.END}")
    print(f"{Colors.CYAN}Ask complex multi-step questions! I have access to {len(agent.list_tools())} tools with sophisticated reasoning.{Colors.END}")
    print(f"{Colors.YELLOW}Commands: 'help' for assistance, 'tools' to list tools, 'quit' to exit{Colors.END}")

    config = RunnableConfig(recursion_limit=30, thread_id="cli_session")
    
    while True:
        try:
            # Get user input
            user_input = input(f"\n{Colors.BLUE}You:{Colors.END} ").strip()
            
            if not user_input:
                continue
                
            # Handle special commands
            if user_input.lower() in ['quit', 'exit', 'q']:
                print(f"{Colors.YELLOW}👋 Goodbye!{Colors.END}")
                break
            elif user_input.lower() in ['help', 'h']:
                print_help()
                continue
            elif user_input.lower() in ['tools', 'list']:
                print_tools(agent)
                continue
            elif user_input.lower() in ['clear', 'cls']:
                os.system('cls' if os.name == 'nt' else 'clear')
                print_banner()
                continue
            
            # Process the query
            print(f"{Colors.PURPLE}Agent:{Colors.END} ", end="", flush=True)
            
            try:
                response = await agent.invoke(user_input, config)
                if response and "messages" in response:
                    content = response["messages"][-1].content
                    print(f"{content}")
                else:
                    print(f"{Colors.RED}No response received{Colors.END}")
                    
            except Exception as e:
                print(f"{Colors.RED}Error: {e}{Colors.END}")
                
        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}👋 Goodbye!{Colors.END}")
            break
        except EOFError:
            print(f"\n{Colors.YELLOW}👋 Goodbye!{Colors.END}")
            break

async def single_query_mode(agent, query: str, stream: bool = False):
    """Run a single query and exit."""
    print(f"\n{Colors.BLUE}Query:{Colors.END} {query}")
    print(f"{Colors.PURPLE}Response:{Colors.END}")
    
    try:
        if stream:
            # Stream the response
            async for chunk in agent.stream(query):
                if "messages" in chunk and chunk["messages"]:
                    last_message = chunk["messages"][-1]
                    if hasattr(last_message, 'content') and last_message.content:
                        print(last_message.content, end="", flush=True)
            print()  # New line after streaming
        else:
            # Get complete response
            response = await agent.invoke(query)
            if response and "messages" in response:
                content = response["messages"][-1].content
                print(f"{content}")
            else:
                print(f"{Colors.RED}No response received{Colors.END}")
                
    except Exception as e:
        print(f"{Colors.RED}Error: {e}{Colors.END}")
        return False
    
    return True

def print_help():
    """Print help information."""
    help_text = f"""
{Colors.GREEN}🔧 Available Commands:{Colors.END}

{Colors.YELLOW}Interactive Mode Commands:{Colors.END}
  help, h     - Show this help message
  tools, list - List available MCP tools
  clear, cls  - Clear the screen
  quit, q     - Exit the program

{Colors.YELLOW}Example Queries:{Colors.END}
  "What's 25 * 4 + 100?"
  "Convert 'hello world' to uppercase and hash it"
  "Validate this JSON: {{'name': 'test', 'value': 123}}"
  "Analyze this crypto address: 0x742d35Cc..."
  "Encode 'secret message' in base64"
  "List all available tools"

{Colors.YELLOW}CLI Options:{Colors.END}
  --query "text"    - Run single query and exit
  --stream          - Stream responses in real-time
  --tools           - List available tools and exit
  --interactive     - Start interactive mode (default)
"""
    print(help_text)

async def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="LangGraph MCP Agent CLI - Interactive Chat Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python cli.py                                    # Interactive chat (default)
  python cli.py --query "What's 5 * 10?"         # Single query and exit
  python cli.py --tools                          # List tools and exit
        """
    )

    parser.add_argument(
        "--query", "-q",
        type=str,
        help="Run a single query and exit (non-interactive)"
    )

    parser.add_argument(
        "--tools", "-t",
        action="store_true",
        help="List available tools and exit"
    )

    parser.add_argument(
        "--no-banner",
        action="store_true",
        help="Don't show the banner"
    )

    parser.add_argument(
        "--stream",
        action="store_true",
        help="Enable streaming for single queries"
    )

    args = parser.parse_args()

    # Show banner unless disabled
    if not args.no_banner:
        print_banner()

    # Check environment
    if not os.getenv("OPENROUTER_API_KEY"):
        print(f"{Colors.RED}❌ Error: OPENROUTER_API_KEY not found in environment variables{Colors.END}")
        print(f"{Colors.YELLOW}Please make sure your .env file is configured correctly.{Colors.END}")
        return 1

    # Initialize agent
    print(f"{Colors.CYAN}🚀 Initializing LangGraph MCP Agent...{Colors.END}")

    agent = None
    try:
        # Use enhanced agent by default, fallback to simple agent if needed
        try:
            agent = await create_enhanced_agent()
            print(f"{Colors.GREEN}✅ Enhanced multi-step reasoning agent initialized{Colors.END}")
        except Exception as e:
            print(f"{Colors.YELLOW}⚠️  Enhanced agent failed, using simple agent: {e}{Colors.END}")
            agent = await create_agent()
        print(f"{Colors.GREEN}✅ Agent initialized successfully{Colors.END}")

        # Handle special modes (non-interactive)
        if args.tools:
            print_tools(agent)
            return 0

        elif args.query:
            success = await single_query_mode(agent, args.query, args.stream)
            return 0 if success else 1

        else:
            # Default: Start interactive chat mode
            await interactive_mode(agent)
            return 0

    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}👋 Interrupted by user{Colors.END}")
        return 0
    except Exception as e:
        print(f"{Colors.RED}❌ Error initializing agent: {e}{Colors.END}")
        return 1
    finally:
        if agent:
            await agent.cleanup()

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}👋 Goodbye!{Colors.END}")
        sys.exit(0)
