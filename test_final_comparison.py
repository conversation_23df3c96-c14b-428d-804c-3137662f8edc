#!/usr/bin/env python3
"""
Final test to demonstrate the improved address analysis.
"""

import asyncio
import sys
from enhanced_agent import create_enhanced_agent
from langchain_core.runnables import RunnableConfig

async def test_improved_analysis():
    """Test the improved address analysis."""
    print("🎉 FINAL TEST: Improved Address Analysis")
    print("=" * 50)
    
    try:
        # Initialize agent
        print("Initializing enhanced agent...")
        agent = await create_enhanced_agent()
        print("✅ Agent initialized successfully\n")
        
        # Test the same address that was giving short responses
        address = "HujryLm7hPMSr1mh6W2mtoLFttZbTnarq9hkXxqimjQL"
        
        print(f"🎯 Testing Solana Wallet Analysis")
        print(f"Address: {address}")
        print("-" * 60)
        
        config = RunnableConfig(recursion_limit=50, thread_id="final_test")
        
        # Use balanced profile for good analysis
        result = await agent.invoke(address, execution_profile="balanced", config=config)
        
        response = result.get('final_response', 'No response')
        
        print(f"✅ Analysis completed!")
        print(f"Response length: {len(response)} characters")
        print(f"Intent: {result.get('intent', 'unknown')}")
        print(f"Execution mode: {result.get('execution_mode', 'unknown')}")
        print(f"Reflection depth: {result.get('reflection_depth', 0)}")
        
        # Show reasoning process
        reasoning = result.get('reasoning_history', [])
        if reasoning:
            print(f"\n🧠 Dynamic Reasoning Process ({len(reasoning)} steps):")
            for i, step in enumerate(reasoning, 1):
                print(f"  {i}. {step}")
        
        # Show the full response to demonstrate quality
        print(f"\n📄 COMPLETE ANALYSIS RESPONSE:")
        print("=" * 60)
        print(response)
        print("=" * 60)
        
        # Quality assessment
        quality_indicators = [
            "SOL" in response,
            "token" in response.lower(),
            "portfolio" in response.lower() or "holdings" in response.lower(),
            "trading" in response.lower() or "activity" in response.lower(),
            "analysis" in response.lower(),
            "wallet" in response.lower(),
            len(response) > 800  # Should be substantial
        ]
        
        quality_score = sum(quality_indicators)
        print(f"\n📊 QUALITY ASSESSMENT: {quality_score}/7")
        
        if quality_score >= 6:
            print("🎉 EXCELLENT: Comprehensive professional-grade analysis!")
        elif quality_score >= 4:
            print("✅ GOOD: Solid analysis with room for improvement")
        else:
            print("⚠️  NEEDS WORK: Analysis quality below expectations")
        
        # Compare with previous short response
        print(f"\n📈 IMPROVEMENT COMPARISON:")
        print(f"Previous response: ~45 characters (very short)")
        print(f"Current response: {len(response)} characters ({len(response)//45}x longer!)")
        print(f"Previous reasoning: 1 step")
        print(f"Current reasoning: {len(reasoning)} steps")
        print(f"Previous quality: Poor (no real analysis)")
        print(f"Current quality: {quality_score}/7 (comprehensive analysis)")
        
        await agent.cleanup()
        print("\n🎉 IMPROVEMENT SUCCESSFUL!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the final improvement test."""
    success = await test_improved_analysis()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
