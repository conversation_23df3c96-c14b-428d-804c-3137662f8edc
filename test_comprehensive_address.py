#!/usr/bin/env python3
"""
Test the enhanced comprehensive address analysis.
"""

import asyncio
import sys
from enhanced_agent import create_enhanced_agent
from langchain_core.runnables import RunnableConfig

async def test_comprehensive_address_analysis():
    """Test the enhanced address analysis system."""
    print("🧪 Testing Comprehensive Address Analysis")
    print("=" * 50)
    
    try:
        # Initialize agent
        print("Initializing enhanced agent...")
        agent = await create_enhanced_agent()
        print("✅ Agent initialized successfully\n")
        
        # Test the problematic addresses
        test_cases = [
            {
                "address": "HujryLm7hPMSr1mh6W2mtoLFttZbTnarq9hkXxqimjQL",
                "description": "Solana wallet (should give detailed portfolio analysis)"
            },
            {
                "address": "EUxS8Kqvbe8zDr8bG1RYtgjVNzEhMTG6B6H3WwrCHfwA", 
                "description": "Solana token (should give detailed token analysis)"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            address = test_case["address"]
            description = test_case["description"]
            
            print(f"🎯 Test {i}: {description}")
            print(f"Address: {address}")
            print("-" * 60)
            
            config = RunnableConfig(recursion_limit=50, thread_id=f"test_comprehensive_{i}")
            
            try:
                # Use thorough profile for maximum analysis
                result = await agent.invoke(address, execution_profile="thorough", config=config)
                
                response = result.get('final_response', 'No response')
                
                print(f"✅ Analysis completed!")
                print(f"Response length: {len(response)} characters")
                print(f"Intent: {result.get('intent', 'unknown')}")
                print(f"Mode: {result.get('execution_mode', 'unknown')}")
                print(f"Next Action: {result.get('next_action', 'unknown')}")
                print(f"Reflection depth: {result.get('reflection_depth', 0)}")
                
                # Show reasoning process
                reasoning = result.get('reasoning_history', [])
                if reasoning:
                    print(f"\n🧠 Reasoning Process ({len(reasoning)} steps):")
                    for j, step in enumerate(reasoning[:5], 1):  # Show first 5 steps
                        print(f"  {j}. {step}")
                    if len(reasoning) > 5:
                        print(f"  ... and {len(reasoning) - 5} more steps")
                
                # Show first part of response to verify quality
                print(f"\n📄 Response Preview (first 800 characters):")
                print("-" * 40)
                print(response[:800])
                if len(response) > 800:
                    print("...")
                print("-" * 40)
                
                # Quality assessment
                quality_indicators = [
                    "USD" in response or "$" in response,
                    "%" in response,
                    "holders" in response.lower() or "portfolio" in response.lower(),
                    "trading" in response.lower() or "transaction" in response.lower(),
                    "analysis" in response.lower(),
                    "risk" in response.lower(),
                    len(response) > 500  # Should be substantial
                ]
                
                quality_score = sum(quality_indicators)
                print(f"\n📊 Quality Assessment: {quality_score}/7")
                
                if quality_score >= 5:
                    print("✅ HIGH QUALITY: Comprehensive analysis detected!")
                elif quality_score >= 3:
                    print("⚠️  MEDIUM QUALITY: Good analysis but could be more detailed")
                else:
                    print("❌ LOW QUALITY: Analysis needs significant improvement")
                
            except Exception as e:
                print(f"❌ Error analyzing {address}: {e}")
                import traceback
                traceback.print_exc()
            
            print("\n" + "="*60 + "\n")
        
        await agent.cleanup()
        print("✅ All comprehensive analysis tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the comprehensive analysis test."""
    success = await test_comprehensive_address_analysis()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
