#!/usr/bin/env python3
"""
Advanced Self-Healing Mechanisms for AP3X Crypto Agent
Comprehensive health monitoring, failure detection, and automatic recovery system.
"""

import asyncio
import time
import json
import logging
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import threading
from collections import defaultdict, deque
import psutil
import aiohttp

class HealthStatus(Enum):
    """Health status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CRITICAL = "critical"
    RECOVERING = "recovering"

class FailureType(Enum):
    """Types of failures that can be detected."""
    CONNECTION_FAILURE = "connection_failure"
    TIMEOUT = "timeout"
    API_ERROR = "api_error"
    TOOL_FAILURE = "tool_failure"
    MEMORY_PRESSURE = "memory_pressure"
    RATE_LIMIT = "rate_limit"
    AUTHENTICATION_ERROR = "auth_error"
    NETWORK_ERROR = "network_error"
    RESOURCE_EXHAUSTION = "resource_exhaustion"

@dataclass
class HealthMetrics:
    """Health metrics for a component."""
    component_name: str
    status: HealthStatus = HealthStatus.HEALTHY
    last_check: datetime = field(default_factory=datetime.now)
    response_time: float = 0.0
    error_count: int = 0
    success_count: int = 0
    uptime: float = 0.0
    memory_usage: float = 0.0
    cpu_usage: float = 0.0
    custom_metrics: Dict[str, Any] = field(default_factory=dict)

@dataclass
class FailureEvent:
    """Represents a failure event."""
    timestamp: datetime
    component: str
    failure_type: FailureType
    severity: str
    message: str
    context: Dict[str, Any] = field(default_factory=dict)
    resolved: bool = False
    resolution_time: Optional[datetime] = None

@dataclass
class RecoveryAction:
    """Represents a recovery action."""
    name: str
    action: Callable
    priority: int = 1
    max_retries: int = 3
    retry_delay: float = 1.0
    conditions: List[Callable] = field(default_factory=list)

class CircuitBreaker:
    """Circuit breaker pattern implementation."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0, expected_exception: type = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        
    async def call(self, func, *args, **kwargs):
        """Execute function with circuit breaker protection."""
        if self.state == "OPEN":
            if self._should_attempt_reset():
                self.state = "HALF_OPEN"
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset."""
        return (self.last_failure_time and 
                time.time() - self.last_failure_time >= self.recovery_timeout)
    
    def _on_success(self):
        """Handle successful execution."""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def _on_failure(self):
        """Handle failed execution."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"

class HealthMonitor:
    """Real-time health monitoring system."""
    
    def __init__(self, check_interval: float = 30.0):
        self.check_interval = check_interval
        self.components: Dict[str, HealthMetrics] = {}
        self.failure_history: deque = deque(maxlen=1000)
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.recovery_actions: Dict[str, List[RecoveryAction]] = defaultdict(list)
        self.monitoring_active = False
        self.monitor_task = None
        self.logger = logging.getLogger(__name__)
        
    def register_component(self, name: str, health_check: Callable = None) -> None:
        """Register a component for health monitoring."""
        self.components[name] = HealthMetrics(component_name=name)
        if health_check:
            self.register_health_check(name, health_check)
        
    def register_health_check(self, component: str, health_check: Callable) -> None:
        """Register a health check function for a component."""
        if not hasattr(self, '_health_checks'):
            self._health_checks = {}
        self._health_checks[component] = health_check
        
    def register_circuit_breaker(self, component: str, failure_threshold: int = 5, 
                                recovery_timeout: float = 60.0) -> CircuitBreaker:
        """Register a circuit breaker for a component."""
        breaker = CircuitBreaker(failure_threshold, recovery_timeout)
        self.circuit_breakers[component] = breaker
        return breaker
        
    def register_recovery_action(self, component: str, action: RecoveryAction) -> None:
        """Register a recovery action for a component."""
        self.recovery_actions[component].append(action)
        # Sort by priority (higher priority first)
        self.recovery_actions[component].sort(key=lambda x: x.priority, reverse=True)
        
    async def start_monitoring(self) -> None:
        """Start the health monitoring loop."""
        if self.monitoring_active:
            return
            
        self.monitoring_active = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("Health monitoring started")
        
    async def stop_monitoring(self) -> None:
        """Stop the health monitoring loop."""
        self.monitoring_active = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        self.logger.info("Health monitoring stopped")
        
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                await self._check_all_components()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(5)  # Brief pause before retrying
                
    async def _check_all_components(self) -> None:
        """Check health of all registered components."""
        tasks = []
        for component_name in self.components.keys():
            task = asyncio.create_task(self._check_component_health(component_name))
            tasks.append(task)
            
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
            
    async def _check_component_health(self, component: str) -> None:
        """Check health of a specific component."""
        metrics = self.components[component]
        start_time = time.time()
        
        try:
            # Run custom health check if available
            if hasattr(self, '_health_checks') and component in self._health_checks:
                health_result = await self._run_health_check(component)
                if health_result:
                    metrics.status = HealthStatus.HEALTHY
                    metrics.success_count += 1
                else:
                    metrics.status = HealthStatus.UNHEALTHY
                    metrics.error_count += 1
                    await self._handle_component_failure(component, FailureType.TOOL_FAILURE, "Health check failed")
            
            # Update response time
            metrics.response_time = time.time() - start_time
            metrics.last_check = datetime.now()
            
            # Check system resources
            await self._check_system_resources(component, metrics)
            
        except Exception as e:
            metrics.status = HealthStatus.CRITICAL
            metrics.error_count += 1
            await self._handle_component_failure(component, FailureType.CONNECTION_FAILURE, str(e))
            
    async def _run_health_check(self, component: str) -> bool:
        """Run health check for a component."""
        try:
            health_check = self._health_checks[component]
            if asyncio.iscoroutinefunction(health_check):
                return await health_check()
            else:
                return health_check()
        except Exception as e:
            self.logger.error(f"Health check failed for {component}: {e}")
            return False
            
    async def _check_system_resources(self, component: str, metrics: HealthMetrics) -> None:
        """Check system resource usage."""
        try:
            # Memory usage
            memory = psutil.virtual_memory()
            metrics.memory_usage = memory.percent
            
            # CPU usage
            metrics.cpu_usage = psutil.cpu_percent(interval=0.1)
            
            # Check for resource pressure
            if metrics.memory_usage > 90:
                await self._handle_component_failure(component, FailureType.MEMORY_PRESSURE, 
                                                   f"High memory usage: {metrics.memory_usage}%")
            
        except Exception as e:
            self.logger.warning(f"Could not check system resources: {e}")
            
    async def _handle_component_failure(self, component: str, failure_type: FailureType, message: str) -> None:
        """Handle component failure and trigger recovery."""
        failure_event = FailureEvent(
            timestamp=datetime.now(),
            component=component,
            failure_type=failure_type,
            severity="high" if failure_type in [FailureType.CRITICAL, FailureType.RESOURCE_EXHAUSTION] else "medium",
            message=message
        )
        
        self.failure_history.append(failure_event)
        self.logger.warning(f"Component failure detected: {component} - {failure_type.value} - {message}")
        
        # Trigger recovery actions
        await self._trigger_recovery(component, failure_event)
        
    async def _trigger_recovery(self, component: str, failure_event: FailureEvent) -> None:
        """Trigger recovery actions for a failed component."""
        if component not in self.recovery_actions:
            return
            
        for action in self.recovery_actions[component]:
            try:
                # Check if action conditions are met
                if all(condition() for condition in action.conditions):
                    self.logger.info(f"Executing recovery action: {action.name} for {component}")
                    
                    # Execute recovery action with retries
                    success = await self._execute_recovery_action(action)
                    if success:
                        failure_event.resolved = True
                        failure_event.resolution_time = datetime.now()
                        self.logger.info(f"Recovery successful: {action.name}")
                        break
                    else:
                        self.logger.warning(f"Recovery action failed: {action.name}")
                        
            except Exception as e:
                self.logger.error(f"Error executing recovery action {action.name}: {e}")
                
    async def _execute_recovery_action(self, action: RecoveryAction) -> bool:
        """Execute a recovery action with retries."""
        for attempt in range(action.max_retries):
            try:
                if asyncio.iscoroutinefunction(action.action):
                    await action.action()
                else:
                    action.action()
                return True
            except Exception as e:
                self.logger.warning(f"Recovery action {action.name} attempt {attempt + 1} failed: {e}")
                if attempt < action.max_retries - 1:
                    await asyncio.sleep(action.retry_delay)
                    
        return False
        
    def get_health_summary(self) -> Dict[str, Any]:
        """Get overall health summary."""
        total_components = len(self.components)
        healthy_count = sum(1 for m in self.components.values() if m.status == HealthStatus.HEALTHY)
        
        recent_failures = [f for f in self.failure_history if f.timestamp > datetime.now() - timedelta(hours=1)]
        
        return {
            "overall_status": "healthy" if healthy_count == total_components else "degraded",
            "total_components": total_components,
            "healthy_components": healthy_count,
            "recent_failures": len(recent_failures),
            "circuit_breakers": {name: breaker.state for name, breaker in self.circuit_breakers.items()},
            "components": {name: {
                "status": metrics.status.value,
                "last_check": metrics.last_check.isoformat(),
                "response_time": metrics.response_time,
                "error_rate": metrics.error_count / max(metrics.success_count + metrics.error_count, 1)
            } for name, metrics in self.components.items()}
        }

class AdaptiveFallbackSystem:
    """Intelligent fallback system that adapts based on failure patterns."""

    def __init__(self):
        self.fallback_chains: Dict[str, List[str]] = {}
        self.performance_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.failure_patterns: Dict[str, Dict[FailureType, int]] = defaultdict(lambda: defaultdict(int))
        self.logger = logging.getLogger(__name__)

    def register_fallback_chain(self, primary: str, fallbacks: List[str]) -> None:
        """Register a fallback chain for a service."""
        self.fallback_chains[primary] = fallbacks
        self.logger.info(f"Registered fallback chain for {primary}: {fallbacks}")

    def record_performance(self, service: str, response_time: float, success: bool) -> None:
        """Record performance metrics for adaptive decision making."""
        self.performance_history[service].append({
            "timestamp": time.time(),
            "response_time": response_time,
            "success": success
        })

    def record_failure_pattern(self, service: str, failure_type: FailureType) -> None:
        """Record failure patterns for predictive fallback."""
        self.failure_patterns[service][failure_type] += 1

    def get_best_service(self, primary: str) -> str:
        """Get the best available service based on current conditions."""
        candidates = [primary] + self.fallback_chains.get(primary, [])

        # Score each candidate based on recent performance
        scores = {}
        for candidate in candidates:
            scores[candidate] = self._calculate_service_score(candidate)

        # Return the highest scoring service
        best_service = max(scores.items(), key=lambda x: x[1])[0]

        if best_service != primary:
            self.logger.info(f"Fallback activated: {primary} -> {best_service}")

        return best_service

    def _calculate_service_score(self, service: str) -> float:
        """Calculate a score for a service based on recent performance."""
        history = self.performance_history[service]
        if not history:
            return 0.5  # Neutral score for unknown services

        recent_history = [h for h in history if time.time() - h["timestamp"] < 300]  # Last 5 minutes
        if not recent_history:
            return 0.5

        # Calculate success rate
        success_rate = sum(1 for h in recent_history if h["success"]) / len(recent_history)

        # Calculate average response time (lower is better)
        avg_response_time = sum(h["response_time"] for h in recent_history) / len(recent_history)
        response_score = max(0, 1 - (avg_response_time / 10))  # Normalize to 0-1

        # Combine scores (weighted)
        final_score = (success_rate * 0.7) + (response_score * 0.3)

        return final_score

class AutoRecoveryManager:
    """Manages automatic recovery mechanisms."""

    def __init__(self, health_monitor: HealthMonitor):
        self.health_monitor = health_monitor
        self.recovery_strategies: Dict[str, List[Callable]] = defaultdict(list)
        self.recovery_history: deque = deque(maxlen=500)
        self.logger = logging.getLogger(__name__)

    def register_recovery_strategy(self, component: str, strategy: Callable, priority: int = 1) -> None:
        """Register a recovery strategy for a component."""
        self.recovery_strategies[component].append((strategy, priority))
        # Sort by priority
        self.recovery_strategies[component].sort(key=lambda x: x[1], reverse=True)

    async def attempt_recovery(self, component: str, failure_type: FailureType) -> bool:
        """Attempt to recover a failed component."""
        if component not in self.recovery_strategies:
            self.logger.warning(f"No recovery strategies available for {component}")
            return False

        recovery_start = time.time()

        for strategy, priority in self.recovery_strategies[component]:
            try:
                self.logger.info(f"Attempting recovery strategy for {component} (priority: {priority})")

                if asyncio.iscoroutinefunction(strategy):
                    success = await strategy(component, failure_type)
                else:
                    success = strategy(component, failure_type)

                if success:
                    recovery_time = time.time() - recovery_start
                    self.recovery_history.append({
                        "timestamp": datetime.now(),
                        "component": component,
                        "failure_type": failure_type.value,
                        "recovery_time": recovery_time,
                        "strategy": strategy.__name__,
                        "success": True
                    })

                    self.logger.info(f"Recovery successful for {component} in {recovery_time:.2f}s")
                    return True

            except Exception as e:
                self.logger.error(f"Recovery strategy failed for {component}: {e}")

        # Record failed recovery attempt
        self.recovery_history.append({
            "timestamp": datetime.now(),
            "component": component,
            "failure_type": failure_type.value,
            "recovery_time": time.time() - recovery_start,
            "strategy": "all_failed",
            "success": False
        })

        return False

class SelfHealingAgent:
    """Main self-healing agent that coordinates all healing mechanisms."""

    def __init__(self, check_interval: float = 30.0):
        self.health_monitor = HealthMonitor(check_interval)
        self.fallback_system = AdaptiveFallbackSystem()
        self.recovery_manager = AutoRecoveryManager(self.health_monitor)
        self.logger = logging.getLogger(__name__)

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    async def initialize(self) -> None:
        """Initialize the self-healing system."""
        await self.health_monitor.start_monitoring()
        self.logger.info("Self-healing agent initialized")

    async def shutdown(self) -> None:
        """Shutdown the self-healing system."""
        await self.health_monitor.stop_monitoring()
        self.logger.info("Self-healing agent shutdown")

    def register_component(self, name: str, health_check: Callable = None,
                          fallbacks: List[str] = None, recovery_strategies: List[Callable] = None) -> None:
        """Register a component with full self-healing capabilities."""
        # Register with health monitor
        self.health_monitor.register_component(name, health_check)

        # Register fallback chain
        if fallbacks:
            self.fallback_system.register_fallback_chain(name, fallbacks)

        # Register recovery strategies
        if recovery_strategies:
            for i, strategy in enumerate(recovery_strategies):
                self.recovery_manager.register_recovery_strategy(name, strategy, priority=len(recovery_strategies) - i)

    def get_circuit_breaker(self, component: str, **kwargs) -> CircuitBreaker:
        """Get or create a circuit breaker for a component."""
        return self.health_monitor.register_circuit_breaker(component, **kwargs)

    async def execute_with_healing(self, component: str, func: Callable, *args, **kwargs) -> Any:
        """Execute a function with full self-healing protection."""
        # Get the best available service
        best_service = self.fallback_system.get_best_service(component)

        # Get circuit breaker
        circuit_breaker = self.health_monitor.circuit_breakers.get(best_service)

        start_time = time.time()
        try:
            if circuit_breaker:
                result = await circuit_breaker.call(func, *args, **kwargs)
            else:
                result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)

            # Record successful execution
            response_time = time.time() - start_time
            self.fallback_system.record_performance(best_service, response_time, True)

            return result

        except Exception as e:
            # Record failure
            response_time = time.time() - start_time
            self.fallback_system.record_performance(best_service, response_time, False)

            # Determine failure type
            failure_type = self._classify_failure(e)
            self.fallback_system.record_failure_pattern(best_service, failure_type)

            # Attempt recovery
            recovery_success = await self.recovery_manager.attempt_recovery(best_service, failure_type)

            if not recovery_success:
                # Try fallback services
                fallbacks = self.fallback_system.fallback_chains.get(component, [])
                for fallback in fallbacks:
                    try:
                        self.logger.info(f"Trying fallback service: {fallback}")
                        if asyncio.iscoroutinefunction(func):
                            result = await func(*args, **kwargs)
                        else:
                            result = func(*args, **kwargs)

                        # Record successful fallback
                        fallback_time = time.time() - start_time
                        self.fallback_system.record_performance(fallback, fallback_time, True)
                        return result

                    except Exception as fallback_error:
                        self.logger.warning(f"Fallback {fallback} also failed: {fallback_error}")
                        continue

            # All recovery attempts failed
            raise e

    def _classify_failure(self, exception: Exception) -> FailureType:
        """Classify the type of failure based on the exception."""
        error_msg = str(exception).lower()

        if "timeout" in error_msg or "timed out" in error_msg:
            return FailureType.TIMEOUT
        elif "connection" in error_msg or "network" in error_msg:
            return FailureType.CONNECTION_FAILURE
        elif "rate limit" in error_msg or "too many requests" in error_msg:
            return FailureType.RATE_LIMIT
        elif "auth" in error_msg or "unauthorized" in error_msg:
            return FailureType.AUTHENTICATION_ERROR
        elif "memory" in error_msg:
            return FailureType.MEMORY_PRESSURE
        else:
            return FailureType.API_ERROR

    def get_health_report(self) -> Dict[str, Any]:
        """Get comprehensive health report."""
        health_summary = self.health_monitor.get_health_summary()

        # Add recovery statistics
        recent_recoveries = [r for r in self.recovery_manager.recovery_history
                           if r["timestamp"] > datetime.now() - timedelta(hours=24)]

        health_summary["recovery_stats"] = {
            "total_recoveries_24h": len(recent_recoveries),
            "successful_recoveries_24h": sum(1 for r in recent_recoveries if r["success"]),
            "average_recovery_time": sum(r["recovery_time"] for r in recent_recoveries) / max(len(recent_recoveries), 1)
        }

        return health_summary
