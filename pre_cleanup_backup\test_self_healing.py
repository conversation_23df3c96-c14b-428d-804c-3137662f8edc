#!/usr/bin/env python3
"""
Comprehensive Test Suite for Self-Healing Mechanisms
Tests failure injection, recovery validation, and performance impact assessment.
"""

import asyncio
import pytest
import time
import logging
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any, List

from self_healing import (
    SelfHealingAgent, HealthMonitor, CircuitBreaker, AdaptiveFallbackSystem,
    AutoRecoveryManager, HealthStatus, FailureType
)
from enhanced_agent_healing import SelfHealingEnhancedAgent, create_self_healing_agent

# Setup logging for tests
logging.basicConfig(level=logging.INFO)

class TestHealthMonitor:
    """Test suite for the health monitoring system."""
    
    @pytest.fixture
    async def health_monitor(self):
        """Create a health monitor for testing."""
        monitor = HealthMonitor(check_interval=1.0)
        await monitor.start_monitoring()
        yield monitor
        await monitor.stop_monitoring()
    
    @pytest.mark.asyncio
    async def test_component_registration(self, health_monitor):
        """Test component registration and health checks."""
        # Register a healthy component
        async def healthy_check():
            return True
            
        health_monitor.register_component("test_component", healthy_check)
        
        # Wait for health check
        await asyncio.sleep(1.5)
        
        # Verify component is registered and healthy
        assert "test_component" in health_monitor.components
        assert health_monitor.components["test_component"].status == HealthStatus.HEALTHY
    
    @pytest.mark.asyncio
    async def test_failure_detection(self, health_monitor):
        """Test failure detection and handling."""
        # Register a failing component
        async def failing_check():
            return False
            
        health_monitor.register_component("failing_component", failing_check)
        
        # Wait for health check
        await asyncio.sleep(1.5)
        
        # Verify failure is detected
        assert health_monitor.components["failing_component"].status == HealthStatus.UNHEALTHY
        assert len(health_monitor.failure_history) > 0
    
    @pytest.mark.asyncio
    async def test_recovery_action_execution(self, health_monitor):
        """Test recovery action execution."""
        recovery_called = False
        
        async def recovery_action():
            nonlocal recovery_called
            recovery_called = True
            
        # Register component with recovery action
        from self_healing import RecoveryAction
        action = RecoveryAction("test_recovery", recovery_action, priority=1)
        health_monitor.register_recovery_action("test_component", action)
        
        # Trigger failure
        await health_monitor._handle_component_failure(
            "test_component", FailureType.CONNECTION_FAILURE, "Test failure"
        )
        
        # Wait for recovery
        await asyncio.sleep(0.5)
        
        # Verify recovery was called
        assert recovery_called

class TestCircuitBreaker:
    """Test suite for circuit breaker functionality."""
    
    def test_circuit_breaker_closed_state(self):
        """Test circuit breaker in closed state."""
        breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=5.0)
        
        # Should be closed initially
        assert breaker.state == "CLOSED"
        
        # Successful calls should keep it closed
        result = asyncio.run(breaker.call(lambda: "success"))
        assert result == "success"
        assert breaker.state == "CLOSED"
    
    def test_circuit_breaker_open_state(self):
        """Test circuit breaker opening after failures."""
        breaker = CircuitBreaker(failure_threshold=2, recovery_timeout=5.0)
        
        # Cause failures to open the breaker
        for _ in range(2):
            try:
                asyncio.run(breaker.call(lambda: exec('raise Exception("test error")')))
            except:
                pass
        
        # Should be open now
        assert breaker.state == "OPEN"
        
        # Should raise exception when open
        with pytest.raises(Exception, match="Circuit breaker is OPEN"):
            asyncio.run(breaker.call(lambda: "should fail"))
    
    def test_circuit_breaker_half_open_state(self):
        """Test circuit breaker half-open state and recovery."""
        breaker = CircuitBreaker(failure_threshold=1, recovery_timeout=0.1)
        
        # Cause failure to open
        try:
            asyncio.run(breaker.call(lambda: exec('raise Exception("test error")')))
        except:
            pass
        
        assert breaker.state == "OPEN"
        
        # Wait for recovery timeout
        time.sleep(0.2)
        
        # Next call should put it in half-open
        result = asyncio.run(breaker.call(lambda: "success"))
        assert result == "success"
        assert breaker.state == "CLOSED"  # Should close on success

class TestAdaptiveFallbackSystem:
    """Test suite for adaptive fallback system."""
    
    def test_fallback_chain_registration(self):
        """Test fallback chain registration."""
        fallback_system = AdaptiveFallbackSystem()
        
        fallback_system.register_fallback_chain("primary", ["fallback1", "fallback2"])
        
        assert "primary" in fallback_system.fallback_chains
        assert fallback_system.fallback_chains["primary"] == ["fallback1", "fallback2"]
    
    def test_performance_recording(self):
        """Test performance metrics recording."""
        fallback_system = AdaptiveFallbackSystem()
        
        # Record some performance data
        fallback_system.record_performance("service1", 0.5, True)
        fallback_system.record_performance("service1", 1.0, False)
        
        assert len(fallback_system.performance_history["service1"]) == 2
    
    def test_best_service_selection(self):
        """Test best service selection based on performance."""
        fallback_system = AdaptiveFallbackSystem()
        
        # Register fallback chain
        fallback_system.register_fallback_chain("primary", ["fallback1"])
        
        # Record poor performance for primary
        for _ in range(5):
            fallback_system.record_performance("primary", 5.0, False)
        
        # Record good performance for fallback
        for _ in range(5):
            fallback_system.record_performance("fallback1", 0.5, True)
        
        # Should select fallback
        best = fallback_system.get_best_service("primary")
        assert best == "fallback1"

class TestAutoRecoveryManager:
    """Test suite for automatic recovery manager."""
    
    @pytest.fixture
    def recovery_manager(self):
        """Create a recovery manager for testing."""
        health_monitor = HealthMonitor()
        return AutoRecoveryManager(health_monitor)
    
    @pytest.mark.asyncio
    async def test_recovery_strategy_registration(self, recovery_manager):
        """Test recovery strategy registration."""
        async def test_strategy(component, failure_type):
            return True
            
        recovery_manager.register_recovery_strategy("test_component", test_strategy, priority=1)
        
        assert "test_component" in recovery_manager.recovery_strategies
        assert len(recovery_manager.recovery_strategies["test_component"]) == 1
    
    @pytest.mark.asyncio
    async def test_successful_recovery(self, recovery_manager):
        """Test successful recovery attempt."""
        async def successful_strategy(component, failure_type):
            return True
            
        recovery_manager.register_recovery_strategy("test_component", successful_strategy)
        
        success = await recovery_manager.attempt_recovery("test_component", FailureType.CONNECTION_FAILURE)
        
        assert success
        assert len(recovery_manager.recovery_history) == 1
        assert recovery_manager.recovery_history[0]["success"]
    
    @pytest.mark.asyncio
    async def test_failed_recovery(self, recovery_manager):
        """Test failed recovery attempt."""
        async def failing_strategy(component, failure_type):
            return False
            
        recovery_manager.register_recovery_strategy("test_component", failing_strategy)
        
        success = await recovery_manager.attempt_recovery("test_component", FailureType.CONNECTION_FAILURE)
        
        assert not success
        assert len(recovery_manager.recovery_history) == 1
        assert not recovery_manager.recovery_history[0]["success"]

class TestSelfHealingAgent:
    """Test suite for the main self-healing agent."""
    
    @pytest.fixture
    async def healing_agent(self):
        """Create a self-healing agent for testing."""
        agent = SelfHealingAgent(check_interval=1.0)
        await agent.initialize()
        yield agent
        await agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_component_registration(self, healing_agent):
        """Test full component registration with all features."""
        async def health_check():
            return True
            
        async def recovery_strategy(component, failure_type):
            return True
            
        healing_agent.register_component(
            "test_component",
            health_check=health_check,
            fallbacks=["fallback1"],
            recovery_strategies=[recovery_strategy]
        )
        
        # Verify all systems are configured
        assert "test_component" in healing_agent.health_monitor.components
        assert "test_component" in healing_agent.fallback_system.fallback_chains
        assert "test_component" in healing_agent.recovery_manager.recovery_strategies
    
    @pytest.mark.asyncio
    async def test_execute_with_healing_success(self, healing_agent):
        """Test successful execution with healing protection."""
        async def test_function():
            return "success"
            
        result = await healing_agent.execute_with_healing("test_component", test_function)
        
        assert result == "success"
    
    @pytest.mark.asyncio
    async def test_execute_with_healing_failure_and_recovery(self, healing_agent):
        """Test execution with failure and recovery."""
        call_count = 0
        
        async def test_function():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise Exception("First call fails")
            return "success after recovery"
        
        # Register recovery strategy
        async def recovery_strategy(component, failure_type):
            return True
            
        healing_agent.register_component(
            "test_component",
            recovery_strategies=[recovery_strategy]
        )
        
        # This should fail initially but succeed after recovery
        with pytest.raises(Exception):
            await healing_agent.execute_with_healing("test_component", test_function)

class TestSelfHealingEnhancedAgent:
    """Test suite for the self-healing enhanced agent integration."""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self):
        """Test self-healing agent initialization."""
        # Mock the MCP components to avoid external dependencies
        with patch('enhanced_agent_healing.get_safe_mcp_configs') as mock_configs:
            mock_configs.return_value = {
                "utility_server": {
                    "command": "python",
                    "args": ["simple_mcp_server.py"],
                    "transport": "stdio"
                }
            }
            
            with patch('enhanced_agent_healing.SelfHealingEnhancedAgent._setup_model'):
                with patch('enhanced_agent_healing.SelfHealingEnhancedAgent._setup_mcp_client'):
                    with patch('enhanced_agent_healing.SelfHealingEnhancedAgent._setup_tools_with_healing'):
                        agent = SelfHealingEnhancedAgent()
                        
                        # Should initialize without errors
                        await agent.initialize()
                        
                        # Verify healing agent is initialized
                        assert agent.healing_agent is not None
                        
                        await agent.cleanup()
    
    @pytest.mark.asyncio
    async def test_health_status_reporting(self):
        """Test health status reporting functionality."""
        with patch('enhanced_agent_healing.get_safe_mcp_configs'):
            with patch('enhanced_agent_healing.SelfHealingEnhancedAgent._setup_model'):
                with patch('enhanced_agent_healing.SelfHealingEnhancedAgent._setup_mcp_client'):
                    with patch('enhanced_agent_healing.SelfHealingEnhancedAgent._setup_tools_with_healing'):
                        agent = SelfHealingEnhancedAgent()
                        await agent.initialize()
                        
                        # Get health status
                        health_status = agent.get_health_status()
                        
                        # Should return a valid health report
                        assert isinstance(health_status, dict)
                        assert "overall_status" in health_status
                        assert "total_components" in health_status
                        
                        await agent.cleanup()

class TestFailureInjection:
    """Test suite for failure injection and recovery validation."""
    
    @pytest.mark.asyncio
    async def test_network_failure_simulation(self):
        """Test network failure simulation and recovery."""
        healing_agent = SelfHealingAgent()
        await healing_agent.initialize()
        
        # Simulate network failure
        async def failing_network_call():
            raise ConnectionError("Network unreachable")
        
        # Register with fallback
        healing_agent.register_component(
            "network_service",
            fallbacks=["local_cache"]
        )
        
        # Should handle the failure gracefully
        with pytest.raises(ConnectionError):
            await healing_agent.execute_with_healing("network_service", failing_network_call)
        
        await healing_agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_timeout_failure_simulation(self):
        """Test timeout failure simulation and recovery."""
        healing_agent = SelfHealingAgent()
        await healing_agent.initialize()
        
        # Simulate timeout
        async def slow_operation():
            await asyncio.sleep(10)  # This will timeout
            return "success"
        
        # Should timeout and trigger recovery
        with pytest.raises(asyncio.TimeoutError):
            await asyncio.wait_for(
                healing_agent.execute_with_healing("slow_service", slow_operation),
                timeout=1.0
            )
        
        await healing_agent.shutdown()

# Performance impact tests
class TestPerformanceImpact:
    """Test suite for assessing performance impact of self-healing."""
    
    @pytest.mark.asyncio
    async def test_overhead_measurement(self):
        """Measure overhead of self-healing mechanisms."""
        # Test without healing
        start_time = time.time()
        for _ in range(100):
            await asyncio.sleep(0.001)  # Simulate work
        baseline_time = time.time() - start_time
        
        # Test with healing
        healing_agent = SelfHealingAgent()
        await healing_agent.initialize()
        
        async def test_work():
            await asyncio.sleep(0.001)
            return "done"
        
        start_time = time.time()
        for _ in range(100):
            await healing_agent.execute_with_healing("test_service", test_work)
        healing_time = time.time() - start_time
        
        await healing_agent.shutdown()
        
        # Calculate overhead
        overhead = (healing_time - baseline_time) / baseline_time * 100
        
        # Overhead should be reasonable (less than 50%)
        assert overhead < 50, f"Self-healing overhead too high: {overhead:.2f}%"
        
        print(f"Self-healing overhead: {overhead:.2f}%")

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
