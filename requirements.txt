# Core LangGraph and <PERSON><PERSON>hai<PERSON> dependencies
langgraph>=0.2.74
langchain>=0.3.0
langchain-openai>=0.2.0
langchain-mcp-adapters>=0.1.0

# MCP server framework
fastmcp>=0.1.0

# Additional utilities
python-dotenv>=1.0.0

# Optional: For enhanced functionality
tavily-python>=0.3.0
redis>=5.0.0

# Note: The following MCP servers require external tools:
#
# Node.js/npx servers:
# - @modelcontextprotocol/server-sequential-thinking
# - mcp-remote (for Tavily remote endpoint)
# - @moralisweb3/api-mcp-server
# Make sure you have Node.js and npm installed
#
# Python/uvx servers:
# - duckduckgo-mcp-server (requires uv: pip install uv)
# Make sure you have uv installed for DuckDuckGo search
