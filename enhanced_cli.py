#!/usr/bin/env python3
"""
Enhanced CLI interface for the sophisticated LangGraph MCP Agent
Now with dynamic reasoning, execution profiles, and specialist routing.
"""

import asyncio
import argparse
import sys
import os
from typing import Optional
from dotenv import load_dotenv

from enhanced_agent import create_enhanced_agent, EnhancedLangGraphMCPAgent
from langchain_core.runnables import RunnableConfig

# Load environment variables
load_dotenv()

class Colors:
    """ANSI color codes for terminal output."""
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_banner():
    """Print the enhanced CLI banner."""
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║              Enhanced LangGraph MCP Agent                   ║
║     Dynamic Reasoning + Execution Profiles + Specialists   ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
"""
    print(banner)

def print_reasoning_process(reasoning_history):
    """Print the agent's reasoning process."""
    if reasoning_history:
        print(f"\n{Colors.PURPLE}🧠 Dynamic Reasoning Process:{Colors.END}")
        for i, step in enumerate(reasoning_history, 1):
            print(f"  {Colors.YELLOW}{i}.{Colors.END} {step}")

def print_execution_metadata(result):
    """Print execution metadata including new features."""
    if "execution_profile" in result:
        print(f"\n{Colors.CYAN}⚙️ Execution Metadata:{Colors.END}")
        print(f"  Profile: {result.get('execution_profile', 'balanced')}")
        print(f"  Mode: {result.get('execution_mode', 'unknown')}")
        print(f"  Intent: {result.get('intent', 'unknown')}")
        print(f"  Confidence: {result.get('confidence_level', 0):.2f}")
        
        if "reflection_depth" in result:
            print(f"  Reflection Depth: {result.get('reflection_depth', 0)}")
        if "last_tool_output_quality" in result:
            print(f"  Last Tool Quality: {result.get('last_tool_output_quality', 'unknown')}")

async def interactive_mode(agent: EnhancedLangGraphMCPAgent):
    """Run the enhanced agent in interactive chat mode with execution profiles."""
    print(f"\n{Colors.GREEN}💬 Enhanced Interactive Mode with Dynamic Reasoning{Colors.END}")
    print(f"{Colors.CYAN}Ask complex multi-step questions! The agent now uses dynamic reasoning and reflection.{Colors.END}")
    print(f"{Colors.YELLOW}Commands: 'profile <fast|balanced|thorough>' to set execution profile{Colors.END}")
    print(f"{Colors.YELLOW}          'help' for assistance, 'tools' to list tools, 'quit' to exit{Colors.END}")
    
    config = RunnableConfig(recursion_limit=50, thread_id="enhanced_cli_session")
    current_profile = "balanced"
    
    while True:
        try:
            # Get user input
            user_input = input(f"\n{Colors.BLUE}You ({current_profile}):{Colors.END} ").strip()
            
            if not user_input:
                continue
                
            # Handle special commands
            if user_input.lower() in ['quit', 'exit', 'q']:
                print(f"{Colors.YELLOW}👋 Goodbye!{Colors.END}")
                break
            elif user_input.lower() in ['help', 'h']:
                print_help()
                continue
            elif user_input.lower().startswith('profile '):
                new_profile = user_input[8:].strip().lower()
                if new_profile in ['fast', 'balanced', 'thorough']:
                    current_profile = new_profile
                    print(f"{Colors.GREEN}✅ Execution profile set to: {current_profile}{Colors.END}")
                else:
                    print(f"{Colors.RED}❌ Invalid profile. Use: fast, balanced, or thorough{Colors.END}")
                continue
            elif user_input.lower() in ['tools', 'list']:
                print_tools(agent)
                continue
            elif user_input.lower() in ['clear', 'cls']:
                os.system('cls' if os.name == 'nt' else 'clear')
                print_banner()
                continue
            
            # Process the query with enhanced reasoning and execution profile
            print(f"{Colors.PURPLE}Agent:{Colors.END} Processing with {current_profile} profile and dynamic reasoning...")
            
            try:
                result = await agent.invoke(user_input, execution_profile=current_profile, config=config)
                
                # Display the response
                final_response = result.get("final_response", "No response generated")
                print(f"\n{Colors.GREEN}Response:{Colors.END} {final_response}")
                
                # Display reasoning process
                reasoning_history = result.get("reasoning_history", [])
                print_reasoning_process(reasoning_history)
                
                # Display execution metadata
                print_execution_metadata(result)
                
            except Exception as e:
                print(f"{Colors.RED}❌ Error: {e}{Colors.END}")
                
        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}👋 Interrupted by user{Colors.END}")
            break
        except Exception as e:
            print(f"{Colors.RED}❌ Unexpected error: {e}{Colors.END}")

async def single_query_mode(agent: EnhancedLangGraphMCPAgent, query: str, profile: str = "balanced") -> bool:
    """Process a single query and return success status."""
    try:
        print(f"{Colors.PURPLE}Processing with {profile} profile:{Colors.END} {query}")
        
        config = RunnableConfig(recursion_limit=50, thread_id="enhanced_single_query")
        result = await agent.invoke(query, execution_profile=profile, config=config)
        
        # Display the response
        final_response = result.get("final_response", "No response generated")
        print(f"\n{Colors.GREEN}Response:{Colors.END} {final_response}")
        
        # Display reasoning process
        reasoning_history = result.get("reasoning_history", [])
        print_reasoning_process(reasoning_history)
        
        # Display execution metadata
        print_execution_metadata(result)
        
        return True
        
    except Exception as e:
        print(f"{Colors.RED}❌ Error: {e}{Colors.END}")
        return False

def print_help():
    """Print detailed help information."""
    help_text = f"""
{Colors.CYAN}{Colors.BOLD}Enhanced LangGraph MCP Agent - Help{Colors.END}

{Colors.YELLOW}🎯 Execution Profiles:{Colors.END}
  {Colors.GREEN}fast{Colors.END}       - Quick responses, minimal reflection (good for simple queries)
  {Colors.GREEN}balanced{Colors.END}   - Standard reasoning depth (recommended for most queries)
  {Colors.GREEN}thorough{Colors.END}   - Deep analysis with extensive reflection (best for complex analysis)

{Colors.YELLOW}💬 Chat Commands:{Colors.END}
  {Colors.GREEN}profile <fast|balanced|thorough>{Colors.END} - Set execution profile
  {Colors.GREEN}help{Colors.END}                              - Show this help message
  {Colors.GREEN}tools{Colors.END}                             - List available tools
  {Colors.GREEN}clear{Colors.END}                             - Clear screen and show banner
  {Colors.GREEN}quit{Colors.END}                              - Exit the application

{Colors.YELLOW}🎯 Example Queries:{Colors.END}
  • "Search for Bitcoin news and analyze wallet 0x742d35Cc..."
  • "Get NFTs for wallet abc123 and provide investment advice"
  • "What's the current price of Ethereum and should I buy?"
  • "Analyze my DeFi positions and suggest optimizations"

{Colors.YELLOW}🧠 Dynamic Reasoning Features:{Colors.END}
  • Plan → Act → Reflect loops for robust reasoning
  • Automatic specialist routing for address analysis
  • Quality assessment of tool outputs
  • Adaptive completion based on execution profile
  • Real-time reasoning process visibility

{Colors.CYAN}The agent now dynamically plans each step and reflects on results!{Colors.END}
"""
    print(help_text)

def print_tools(agent: EnhancedLangGraphMCPAgent):
    """Print available tools."""
    tools = agent.list_tools()
    print(f"\n{Colors.CYAN}🛠️  Available Tools ({len(tools)} total):{Colors.END}")
    
    # Group tools by category
    categories = {
        "Utility": [t for t in tools if any(name in t for name in ["add", "multiply", "text", "hash"])],
        "Search": [t for t in tools if any(name in t for name in ["search", "tavily", "duckduckgo"])],
        "Blockchain": [t for t in tools if any(name in t for name in ["evm_", "solana_", "moralis"])],
        "Thinking": [t for t in tools if "thinking" in t]
    }
    
    for category, category_tools in categories.items():
        if category_tools:
            print(f"  {Colors.GREEN}{category} ({len(category_tools)} tools){Colors.END}")
            for tool in category_tools[:3]:
                print(f"    • {tool}")
            if len(category_tools) > 3:
                print(f"    • ... and {len(category_tools) - 3} more")

async def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description="Enhanced LangGraph MCP Agent CLI")
    parser.add_argument("--query", "-q", help="Single query mode")
    parser.add_argument("--profile", "-p", choices=["fast", "balanced", "thorough"], 
                       default="balanced", help="Execution profile")
    parser.add_argument("--tools", "-t", action="store_true", help="List available tools")
    
    args = parser.parse_args()
    
    # Print banner
    print_banner()
    
    # Initialize enhanced agent
    print(f"{Colors.CYAN}🚀 Initializing Enhanced LangGraph MCP Agent...{Colors.END}")
    
    agent = None
    try:
        agent = await create_enhanced_agent()
        print(f"{Colors.GREEN}✅ Enhanced agent with dynamic reasoning initialized successfully{Colors.END}")
        
        # Handle different modes
        if args.tools:
            print_tools(agent)
            return 0
        
        elif args.query:
            success = await single_query_mode(agent, args.query, args.profile)
            return 0 if success else 1
        
        else:
            # Default to interactive mode
            await interactive_mode(agent)
            return 0
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}👋 Interrupted by user{Colors.END}")
        return 0
    except Exception as e:
        print(f"{Colors.RED}❌ Error initializing enhanced agent: {e}{Colors.END}")
        return 1
    finally:
        if agent:
            await agent.cleanup()

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
