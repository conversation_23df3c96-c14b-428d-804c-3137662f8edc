#!/usr/bin/env python3
"""
Test script for the enhanced dynamic reasoning capabilities.
Demonstrates the new Plan → Act → Reflect architecture and execution profiles.
"""

import asyncio
import sys
from enhanced_agent import create_enhanced_agent
from langchain_core.runnables import RunnableConfig

async def test_execution_profiles():
    """Test different execution profiles with the same query."""
    print("🧪 Testing Execution Profiles")
    print("=" * 50)
    
    agent = await create_enhanced_agent()
    config = RunnableConfig(recursion_limit=50, thread_id="test_profiles")
    
    test_query = "What's the current price of Bitcoin and should I invest?"
    
    profiles = ["fast", "balanced", "thorough"]
    
    for profile in profiles:
        print(f"\n🎯 Testing {profile.upper()} Profile:")
        print("-" * 30)
        
        try:
            result = await agent.invoke(test_query, execution_profile=profile, config=config)
            
            print(f"Response: {result.get('final_response', 'No response')[:200]}...")
            print(f"Execution Mode: {result.get('execution_mode', 'unknown')}")
            print(f"Reflection Depth: {result.get('reflection_depth', 0)}")
            print(f"Planning Cycles: {result.get('planning_cycles', 0)}")
            
            reasoning = result.get('reasoning_history', [])
            if reasoning:
                print(f"Reasoning Steps: {len(reasoning)}")
                print(f"Last Step: {reasoning[-1] if reasoning else 'None'}")
                
        except Exception as e:
            print(f"❌ Error with {profile} profile: {e}")
    
    await agent.cleanup()

async def test_specialist_routing():
    """Test automatic specialist routing for address analysis."""
    print("\n🔀 Testing Specialist Routing")
    print("=" * 50)
    
    agent = await create_enhanced_agent()
    config = RunnableConfig(recursion_limit=50, thread_id="test_specialist")
    
    # Test address that should trigger specialist routing
    address_query = "0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b6"
    
    print(f"Testing address query: {address_query}")
    
    try:
        result = await agent.invoke(address_query, config=config)
        
        print(f"Intent: {result.get('intent', 'unknown')}")
        print(f"Next Action: {result.get('next_action', 'unknown')}")
        print(f"Execution Mode: {result.get('execution_mode', 'unknown')}")
        
        reasoning = result.get('reasoning_history', [])
        if reasoning:
            print("Routing Decision:")
            for step in reasoning[:3]:  # Show first 3 reasoning steps
                print(f"  • {step}")
                
    except Exception as e:
        print(f"❌ Error with specialist routing: {e}")
    
    await agent.cleanup()

async def test_dynamic_reflection():
    """Test the dynamic reflection and quality assessment."""
    print("\n🧠 Testing Dynamic Reflection")
    print("=" * 50)
    
    agent = await create_enhanced_agent()
    config = RunnableConfig(recursion_limit=50, thread_id="test_reflection")
    
    # Complex query that should trigger multiple reflection cycles
    complex_query = "Search for Ethereum news, analyze DeFi trends, and provide investment strategy"
    
    print(f"Testing complex query: {complex_query}")
    
    try:
        result = await agent.invoke(complex_query, execution_profile="thorough", config=config)
        
        print(f"Final Response Length: {len(result.get('final_response', ''))}")
        print(f"Reflection Depth: {result.get('reflection_depth', 0)}")
        print(f"Last Tool Quality: {result.get('last_tool_output_quality', 'unknown')}")
        
        reasoning = result.get('reasoning_history', [])
        print(f"\nReasoning Process ({len(reasoning)} steps):")
        for i, step in enumerate(reasoning, 1):
            if "Reflector:" in step or "Dynamic Planner:" in step:
                print(f"  {i}. {step}")
                
        info_gathered = result.get('information_gathered', {})
        print(f"\nInformation Sources: {len(info_gathered)}")
        for source, data in info_gathered.items():
            print(f"  • {source}: {len(str(data))} chars")
                
    except Exception as e:
        print(f"❌ Error with dynamic reflection: {e}")
    
    await agent.cleanup()

async def test_error_recovery():
    """Test error recovery and re-planning capabilities."""
    print("\n🔧 Testing Error Recovery")
    print("=" * 50)
    
    agent = await create_enhanced_agent()
    config = RunnableConfig(recursion_limit=50, thread_id="test_recovery")
    
    # Query that might cause some tools to fail
    error_prone_query = "Get data for invalid_wallet_address_12345"
    
    print(f"Testing error-prone query: {error_prone_query}")
    
    try:
        result = await agent.invoke(error_prone_query, config=config)
        
        print(f"Error Count: {result.get('error_count', 0)}")
        print(f"Final Response: {result.get('final_response', 'No response')[:200]}...")
        
        reasoning = result.get('reasoning_history', [])
        error_steps = [step for step in reasoning if "error" in step.lower() or "failed" in step.lower()]
        
        if error_steps:
            print(f"\nError Recovery Steps:")
            for step in error_steps:
                print(f"  • {step}")
        else:
            print("No explicit error recovery steps found")
                
    except Exception as e:
        print(f"❌ Error with error recovery test: {e}")
    
    await agent.cleanup()

async def test_performance_comparison():
    """Compare performance between different execution modes."""
    print("\n⚡ Testing Performance Comparison")
    print("=" * 50)
    
    agent = await create_enhanced_agent()
    
    simple_query = "What's 2 + 2?"
    
    import time
    
    for profile in ["fast", "balanced"]:
        start_time = time.time()
        
        try:
            config = RunnableConfig(recursion_limit=50, thread_id=f"test_perf_{profile}")
            result = await agent.invoke(simple_query, execution_profile=profile, config=config)
            
            execution_time = time.time() - start_time
            
            print(f"\n{profile.upper()} Profile:")
            print(f"  Execution Time: {execution_time:.2f}s")
            print(f"  Execution Mode: {result.get('execution_mode', 'unknown')}")
            print(f"  Next Action: {result.get('next_action', 'unknown')}")
            print(f"  Response: {result.get('final_response', 'No response')[:100]}...")
            
        except Exception as e:
            print(f"❌ Error with {profile} performance test: {e}")
    
    await agent.cleanup()

async def main():
    """Run all tests to demonstrate enhanced capabilities."""
    print("🚀 Enhanced Dynamic Reasoning Agent - Test Suite")
    print("=" * 60)
    
    try:
        await test_execution_profiles()
        await test_specialist_routing()
        await test_dynamic_reflection()
        await test_error_recovery()
        await test_performance_comparison()
        
        print("\n✅ All tests completed!")
        print("\n🎯 Key Enhancements Demonstrated:")
        print("  • Dynamic Plan → Act → Reflect loops")
        print("  • User-controlled execution profiles")
        print("  • Automatic specialist routing")
        print("  • Quality assessment and reflection")
        print("  • Error recovery and re-planning")
        print("  • Performance optimization")
        
    except Exception as e:
        print(f"❌ Test suite error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
