# 🧠 Enhanced LangGraph MCP Agent

## Multi-Step Reasoning Architecture

This enhanced agent transforms the basic LangGraph MCP setup into a sophisticated multi-step reasoning system that can handle complex queries through structured planning, execution, and synthesis.

## 🏗️ Architecture Overview

### Core Components

1. **Query Router** - Analyzes user intent and creates initial plans
2. **Task Planner** - Core decision-making hub that determines next actions
3. **Tool Execution Engine** - Executes 114 tools across 5 MCP servers
4. **Synthesis Engine** - Combines information into coherent responses

### State Management

```python
class AgentState(TypedDict):
    # Core input/output
    input: str
    intent: str
    final_response: str
    
    # Planning and reasoning
    plan: List[str]
    current_step: int
    reasoning_history: List[str]
    
    # Tool execution tracking
    messages: Annotated[list, add_messages]
    tool_outputs: List[dict]
    
    # Progress tracking
    information_gathered: dict
    next_action: str  # "think", "use_tool", "synthesize", "complete"
    confidence_level: float
    planning_cycles: int
```

## 🔄 Workflow Process

```mermaid
graph TD
    A[User Input] --> B[Query Router]
    B --> C[Task Planner]
    C --> D{Next Action?}
    D -->|use_tool| E[Tool Execution]
    D -->|think| F[Sequential Thinking]
    D -->|synthesize| G[Synthesis Engine]
    E --> C
    F --> C
    G --> H[Final Response]
```

## 🎯 Intent Recognition

The agent automatically detects and handles:

- **blockchain_analysis** - Wallet analysis, NFT data, DeFi positions
- **web_research** - News search, market analysis, price predictions
- **calculation** - Mathematical operations and computations
- **strategic_thinking** - Investment advice, recommendations
- **mixed_query** - Complex multi-step requests combining multiple intents

## 🛠️ Available Tools (114 Total)

### 🔧 Utility Tools (14)
- Math operations, text processing, JSON handling, crypto address analysis

### 🧠 Sequential Thinking (1)
- Advanced reasoning for complex problem-solving

### 🔍 Web Search (6)
- **Tavily**: Primary search with intelligent results
- **DuckDuckGo**: Privacy-focused backup search

### 🌐 Moralis Blockchain API (93)
- NFT data, token balances, wallet analysis, DeFi positions, cross-chain support

## 🚀 Usage Examples

### Simple Usage
```bash
python enhanced_cli.py
```

### Complex Multi-Step Query
```bash
python enhanced_cli.py --query "Search for Bitcoin news, analyze wallet 0x742d35Cc..., and provide strategic recommendations"
```

### Example Complex Query Flow

**Input:** "Search for Ethereum news, analyze wallet ******************************************, and use sequential thinking for investment advice"

**Agent Process:**
1. **Router**: Detects `mixed_query` intent, creates 5-step plan
2. **Planner**: Decides to search for Ethereum news first
3. **Tools**: Executes Tavily search for recent Ethereum news
4. **Planner**: Analyzes progress, decides to get wallet data
5. **Tools**: Executes Moralis wallet analysis
6. **Planner**: Determines complex reasoning needed
7. **Tools**: Calls Sequential Thinking with gathered data
8. **Planner**: Sufficient information gathered, routes to synthesis
9. **Synthesis**: Combines all data into comprehensive response

**Output:** Detailed analysis with market context, wallet breakdown, and strategic recommendations

## 🔧 Key Features

### Multi-Step Planning
- Automatic query decomposition
- Dynamic plan adjustment based on results
- Progress tracking and confidence scoring

### Intelligent Tool Selection
- Context-aware tool choosing
- Automatic fallback between search engines
- Optimal tool sequencing

### Information Synthesis
- Combines data from multiple sources
- Maintains context across tool calls
- Generates coherent final responses

### Error Handling
- Graceful degradation on tool failures
- Loop prevention (max 10 planning cycles)
- Partial results when tools fail

### Reasoning Transparency
- Visible planning process
- Step-by-step reasoning history
- Confidence and metadata tracking

## 📊 Execution Metadata

Each response includes:
- **Intent Classification**: Detected query type
- **Confidence Level**: Agent's confidence in understanding
- **Planning Cycles**: Number of decision cycles
- **Reasoning History**: Step-by-step thought process
- **Information Sources**: What data was gathered from where

## 🎮 Interactive Features

### Enhanced CLI Commands
- `help` - Show detailed help with examples
- `tools` - List tools categorized by function
- `clear` - Clear screen and show banner
- `quit` - Exit gracefully

### Real-Time Feedback
- Planning process visibility
- Information gathering progress
- Reasoning step explanations
- Execution metadata display

## 🔄 Comparison: Simple vs Enhanced Agent

| Feature | Simple Agent | Enhanced Agent |
|---------|-------------|----------------|
| Query Processing | Single-step tool calling | Multi-step reasoning |
| Planning | None | Dynamic planning with confidence |
| Tool Selection | Model decides | Intelligent context-aware selection |
| Information Synthesis | Basic | Comprehensive multi-source synthesis |
| Error Handling | Basic | Graceful degradation with fallbacks |
| Transparency | Limited | Full reasoning process visibility |
| Complex Queries | Struggles | Excels at multi-step analysis |

## 🧪 Testing

Run comprehensive tests:
```bash
python test_enhanced_agent.py
```

Test categories:
- Simple queries
- Web research
- Blockchain analysis
- Complex multi-step reasoning
- Strategic thinking integration
- Error handling
- Loop prevention

## 🔧 Configuration

The enhanced agent uses the same MCP server configuration as the simple agent:

- **Node.js** required for Sequential Thinking, Tavily, Moralis
- **uv** required for DuckDuckGo search
- **API Keys** in `.env`: OPENROUTER_API_KEY, TAVILY_API_KEY, MORALIS_API_KEY

## 🎯 Success Criteria Met

✅ **Multi-Step Reasoning**: Breaks down complex queries systematically
✅ **Cyclical Planning**: Continuous evaluation and re-planning
✅ **Tool Orchestration**: Intelligent sequencing of 114 tools
✅ **Information Synthesis**: Combines data from multiple sources
✅ **Error Resilience**: Graceful handling of failures
✅ **Transparency**: Visible reasoning process
✅ **Backward Compatibility**: Works with existing CLI and tools

## 🚀 Next Steps

The enhanced agent provides a solid foundation for:
- Custom domain-specific reasoning patterns
- Advanced planning algorithms
- Multi-agent collaboration
- Long-term memory integration
- Custom tool development

---

**Ready to experience sophisticated AI reasoning?**
```bash
python enhanced_cli.py
```
