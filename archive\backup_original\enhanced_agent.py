#!/usr/bin/env python3
"""
Enhanced LangGraph MCP Agent with Multi-Step Reasoning
Sophisticated agent architecture with cyclical planning, tool execution, and synthesis.
"""

import os
import re
from typing import Dict, Any, List, TypedDict, Annotated, Optional
from dotenv import load_dotenv

from langchain_openai import ChatOpenAI
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode, create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langchain_core.runnables import RunnableConfig

from mcp_config import get_safe_mcp_configs, print_server_info, clean_config_for_mcp

# Load environment variables
load_dotenv()

class AgentState(TypedDict):
    """Comprehensive state object that tracks the entire reasoning process."""
    # Core input/output
    input: str
    intent: str
    final_response: str

    # Planning and reasoning
    plan: List[str]
    current_step: int
    reasoning_history: List[str]

    # Tool execution tracking
    messages: Annotated[list, add_messages]
    tool_outputs: List[dict]

    # Progress tracking
    information_gathered: dict
    next_action: str  # "think", "use_tool", "synthesize", "complete", "direct_execute"
    confidence_level: float

    # Complexity and execution mode
    complexity_level: str  # "simple", "moderate", "complex"
    execution_mode: str    # "direct", "multi_step"

    # Loop prevention
    planning_cycles: int

class EnhancedLangGraphMCPAgent:
    """Enhanced LangGraph agent with sophisticated multi-step reasoning."""
    
    def __init__(self):
        self.model = None
        self.client = None
        self.tools = []
        self.graph = None
        
    def _setup_model(self):
        """Setup the language model using OpenRouter."""
        api_key = os.getenv("OPENROUTER_API_KEY")
        model_name = os.getenv("OPENROUTER_MODEL", "moonshotai/kimi-k2")
        
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY not found in environment variables")
        
        self.model = ChatOpenAI(
            model=model_name,
            openai_api_key=api_key,
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=4000
        )
        
        print(f"✅ Model configured: {model_name}")
    
    def _setup_mcp_client(self, server_configs: Dict[str, Dict[str, Any]]):
        """Setup MCP client with server configurations."""
        self.client = MultiServerMCPClient(server_configs)
        print(f"✅ MCP client configured with {len(server_configs)} server(s)")
    
    async def initialize(self, server_configs: Dict[str, Dict[str, Any]] = None):
        """Initialize the enhanced agent with model and MCP tools."""
        
        # Setup model
        self._setup_model()
        
        # Default server configuration if none provided
        if server_configs is None:
            server_configs = get_safe_mcp_configs()
            print_server_info(server_configs)
        
        # Clean configuration for MCP client (remove display fields)
        clean_configs = clean_config_for_mcp(server_configs)
        
        # Setup MCP client
        self._setup_mcp_client(clean_configs)
        
        # Get tools from MCP servers with error handling
        try:
            self.tools = await self.client.get_tools()
            print(f"✅ Loaded {len(self.tools)} tools from MCP servers")
            
            # Print tool summary by server
            tool_names = [tool.name for tool in self.tools]
            print(f"🔧 Available tools: {', '.join(tool_names[:10])}" + 
                  (f" and {len(tool_names)-10} more..." if len(tool_names) > 10 else ""))
                  
        except Exception as e:
            print(f"⚠️  Warning: Some MCP servers may not be available: {e}")
            print("🔄 Retrying with minimal configuration...")
            
            # Fallback to minimal configuration
            from mcp_config import MINIMAL_CONFIG
            clean_minimal = clean_config_for_mcp(MINIMAL_CONFIG)
            self._setup_mcp_client(clean_minimal)
            self.tools = await self.client.get_tools()
            print(f"✅ Loaded {len(self.tools)} tools from local server only")
        
        # Build the enhanced graph
        self._build_graph()
        
        print("✅ Enhanced LangGraph agent created successfully")
    
    def _build_graph(self):
        """Build the sophisticated multi-step reasoning graph."""

        # Create the state graph
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("router", self._query_router_node)
        workflow.add_node("planner", self._task_planning_node)
        workflow.add_node("tools", self._enhanced_tool_node)
        workflow.add_node("direct_executor", self._direct_execution_node)
        workflow.add_node("synthesizer", self._synthesis_node)

        # Define the flow
        workflow.set_entry_point("router")

        # Conditional routing from router based on execution mode
        workflow.add_conditional_edges(
            "router",
            self._route_from_router,
            {
                "direct": "direct_executor",
                "multi_step": "planner"
            }
        )

        # Direct execution path
        workflow.add_edge("direct_executor", END)

        # Multi-step execution path
        workflow.add_conditional_edges(
            "planner",
            self._route_from_planner,
            {
                "tools": "tools",
                "synthesizer": "synthesizer"
            }
        )

        # Loop back to planner after tool execution
        workflow.add_edge("tools", "planner")
        workflow.add_edge("synthesizer", END)

        # Compile the graph
        self.graph = workflow.compile()

        # Also create a React agent for proper MCP tool execution
        self._create_react_agent()

        print("✅ Enhanced reasoning graph compiled successfully")

    def _create_react_agent(self):
        """Create a React agent for proper MCP tool execution."""
        if self.tools:
            prompt = (
                "You are an expert blockchain and cryptocurrency analysis agent. "
                "You have access to comprehensive tools for analyzing tokens, wallets, and blockchain data. "
                "Use the appropriate tools to gather accurate, real-time data and provide detailed analysis. "
                "For Solana addresses, first check if they are token contracts using token metadata tools, "
                "then use wallet analysis tools if they are not tokens. "
                "Always use multiple tools to gather comprehensive data before providing analysis."
            )

            self.react_agent = create_react_agent(
                self.model,
                self.tools,
                prompt=prompt,
                checkpointer=MemorySaver()
            )
            print("✅ React agent created for MCP tool execution")
    
    def _assess_query_complexity(self, user_input: str, intent: str, address_info: dict) -> tuple:
        """Assess query complexity and determine execution mode."""

        # Simple patterns that should use direct execution
        simple_patterns = [
            r"^(check|get|show|what.*is.*the)\s+(balance|nft|token|price)",
            r"^(balance|nft|tokens?)\s+(of|for)",
            r"^\d+\s*[\+\-\*\/]\s*\d+",  # Simple math
            r"^what.*is.*\d+.*[\+\-\*\/]",  # Math questions
            r"^search\s+for\s+[^,]+$",  # Simple search without additional requests
            r"^(get|show|list)\s+(nft|token|balance)",
        ]

        # Complex patterns that need multi-step reasoning
        complex_patterns = [
            r"analyz|recommend|strateg|insight|advice|suggest",
            r"compare.*and|versus|vs\.|investment",
            r"portfolio.*optim|risk.*assess|due.*diligence",
            r"should.*i|what.*do.*with|how.*to.*invest",
            r".*and.*(analyz|recommend|strateg)",  # Multiple actions
        ]

        # Check for simple patterns
        for pattern in simple_patterns:
            if re.search(pattern, user_input.lower()):
                return "simple", "direct"

        # Check for complex patterns
        for pattern in complex_patterns:
            if re.search(pattern, user_input.lower()):
                return "complex", "multi_step"

        # Context-based complexity assessment
        words = user_input.lower().split()

        # Simple if:
        # - Single address with clear action context
        if address_info["found"] and any(action in user_input.lower() for action in
                                       ["balance", "nft", "token", "check", "get", "show"]):
            return "simple", "direct"

        # - Short, specific requests
        if len(words) <= 5 and intent in ["calculation", "blockchain_analysis", "web_research"]:
            return "simple", "direct"

        # Complex if:
        # - Address with no context (needs investigation)
        if address_info["found"] and len(words) <= 2:
            return "complex", "multi_step"

        # - Multiple intents or long queries
        if intent == "mixed_query" or len(words) > 10:
            return "complex", "multi_step"

        # - Strategic thinking intent
        if intent == "strategic_thinking":
            return "complex", "multi_step"

        # Default to moderate complexity
        return "moderate", "multi_step"

    def _query_router_node(self, state: AgentState) -> AgentState:
        """Analyze user input to determine intent, complexity, and execution mode."""
        user_input = state.get("input", "").strip()

        # Analyze crypto addresses
        address_info = self._analyze_crypto_address(user_input)

        # Determine intent
        intent = self._determine_intent(user_input, address_info)

        # Assess complexity and execution mode
        complexity_level, execution_mode = self._assess_query_complexity(user_input, intent, address_info)

        # Create appropriate plan based on complexity
        if execution_mode == "direct":
            plan = self._create_direct_plan(intent, user_input, address_info)
            next_action = "direct_execute"
        else:
            plan = self._create_multi_step_plan(intent, user_input, address_info)
            next_action = "use_tool"

        # Set confidence based on clarity and complexity
        confidence = self._calculate_confidence(intent, complexity_level, address_info)

        reasoning = f"Intent: {intent}, Complexity: {complexity_level}, Mode: {execution_mode}"

        return {
            **state,
            "intent": intent,
            "plan": plan,
            "complexity_level": complexity_level,
            "execution_mode": execution_mode,
            "confidence_level": confidence,
            "current_step": 0,
            "planning_cycles": 0,
            "reasoning_history": [reasoning],
            "information_gathered": {},
            "next_action": next_action,
            "messages": [HumanMessage(content=user_input)]
        }
    
    def _determine_intent(self, user_input: str, address_info: dict) -> str:
        """Determine the primary intent of the user query."""

        # Address-only input
        if address_info["found"] and len(user_input.split()) <= 2:
            return "address_analysis"

        # Standard intent analysis patterns
        intent_patterns = {
            "blockchain_analysis": [
                r"wallet|address|nft|token|balance|transaction|defi|portfolio",
                r"0x[a-fA-F0-9]{40}|[13][a-km-zA-HJ-NP-Z1-9]{25,34}",
                r"analyze.*wallet|get.*nft|check.*balance"
            ],
            "web_research": [
                r"search|news|find|latest|recent|information|research",
                r"price.*prediction|market.*analysis|crypto.*news"
            ],
            "calculation": [
                r"calculate|compute|math|percentage|multiply|divide|add",
                r"\d+.*[\+\-\*\/].*\d+|what.*is.*\d+"
            ],
            "strategic_thinking": [
                r"recommend|strategy|analyze|think|reasoning|plan",
                r"investment|advice|should.*i|what.*do"
            ]
        }

        # Determine intent
        detected_intents = []
        for intent_type, patterns in intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, user_input.lower()):
                    detected_intents.append(intent_type)
                    break

        # Classify overall intent
        if len(detected_intents) > 1:
            return "mixed_query"
        elif detected_intents:
            return detected_intents[0]
        else:
            return "general_query"

    def _calculate_confidence(self, intent: str, complexity_level: str, address_info: dict) -> float:
        """Calculate confidence level based on intent clarity and complexity."""
        base_confidence = 0.5

        # Higher confidence for clear intents
        if intent in ["calculation", "blockchain_analysis", "web_research"]:
            base_confidence = 0.8
        elif intent == "address_analysis" and address_info["found"]:
            base_confidence = address_info["confidence"]
        elif intent == "mixed_query":
            base_confidence = 0.7

        # Adjust for complexity
        if complexity_level == "simple":
            base_confidence += 0.1
        elif complexity_level == "complex":
            base_confidence -= 0.1

        return min(0.95, max(0.3, base_confidence))

    def _create_direct_plan(self, intent: str, user_input: str, address_info: dict) -> List[str]:
        """Create a simple, direct execution plan."""

        if intent == "calculation":
            return ["Perform calculation", "Return result"]

        elif intent == "blockchain_analysis":
            if address_info["found"]:
                blockchain = address_info.get("blockchain", "ethereum")
                if "balance" in user_input.lower():
                    return [f"Get {blockchain} wallet balance"]
                elif "nft" in user_input.lower():
                    return [f"Get {blockchain} NFT collection"]
                elif "token" in user_input.lower():
                    return [f"Get {blockchain} token holdings"]
                else:
                    return [f"Get {blockchain} wallet overview"]
            else:
                return ["Get blockchain data"]

        elif intent == "web_research":
            return ["Search for information", "Return results"]

        else:
            return ["Execute request", "Return response"]

    def _create_multi_step_plan(self, intent: str, user_input: str, address_info: dict) -> List[str]:
        """Create a comprehensive multi-step plan for complex queries."""

        if intent == "address_analysis":
            return self._create_address_analysis_plan(address_info)

        elif intent == "mixed_query":
            return [
                "Break down the complex query",
                "Gather web research data",
                "Collect blockchain data",
                "Apply comprehensive strategic analysis",
                "Synthesize multi-source response"
            ]

        elif intent == "strategic_thinking":
            return [
                "Gather relevant data",
                "Apply sequential thinking for deep analysis",
                "Generate strategic recommendations"
            ]

        elif intent == "blockchain_analysis":
            return [
                "Extract wallet addresses or contract addresses",
                "Gather comprehensive blockchain data",
                "Apply strategic analysis to understand patterns",
                "Provide comprehensive insights"
            ]

        elif intent == "web_research":
            return [
                "Search for relevant information",
                "Extract key findings",
                "Apply analysis to synthesize insights",
                "Summarize results with context"
            ]

        else:
            return ["Analyze request", "Apply reasoning", "Provide response"]

    def _create_address_analysis_plan(self, address_info: dict) -> List[str]:
        """Create comprehensive analysis plan for crypto addresses."""
        blockchain = address_info.get("blockchain", "unknown")
        address = address_info.get("address", "")

        if blockchain == "solana":
            # Solana addresses could be wallets OR token contracts - comprehensive analysis
            return [
                f"Step 1: Get token metadata for {address} (check if it's a token contract)",
                "Step 2: Get token price and market data if it's a token",
                "Step 3: Analyze trading pairs and liquidity distribution",
                "Step 4: Examine recent trading activity and patterns",
                "Step 5: Analyze holder distribution and concentration",
                "Step 6: If not a token, get comprehensive wallet portfolio",
                "Step 7: Verify native SOL balance",
                "Step 8: Analyze wallet trading history and patterns",
                "Step 9: Apply strategic analysis to synthesize findings",
                "Step 10: Provide comprehensive token/wallet analysis with insights"
            ]
        elif blockchain == "ethereum":
            return [
                f"Step 1: Determine if {address} is wallet, token contract, or NFT contract",
                "Step 2: Get appropriate data (wallet balance, token info, or NFT collection)",
                "Step 3: Analyze transaction history and patterns",
                "Step 4: Check for DeFi positions if wallet",
                "Step 5: Apply strategic analysis",
                "Step 6: Provide comprehensive summary"
            ]
        else:
            return [
                f"Step 1: Analyze {blockchain} address {address}",
                "Step 2: Determine address type (wallet/contract/token)",
                "Step 3: Gather appropriate data based on type",
                "Step 4: Apply strategic analysis",
                "Step 5: Provide comprehensive summary"
            ]

    def _create_initial_plan(self, intent: str, user_input: str) -> List[str]:
        """Create initial plan based on detected intent."""
        plans = {
            "blockchain_analysis": [
                "Extract wallet addresses or contract addresses",
                "Gather blockchain data (NFTs, tokens, transactions)",
                "Apply strategic analysis to understand patterns",
                "Provide comprehensive insights"
            ],
            "web_research": [
                "Search for relevant information",
                "Extract key findings",
                "Apply analysis to synthesize insights",
                "Summarize results with context"
            ],
            "calculation": [
                "Identify mathematical operations needed",
                "Perform calculations",
                "Present results with explanation"
            ],
            "strategic_thinking": [
                "Gather relevant data",
                "Apply sequential thinking for deep analysis",
                "Generate strategic recommendations"
            ],
            "mixed_query": [
                "Break down the complex query",
                "Gather web research data",
                "Collect blockchain data",
                "Apply comprehensive strategic analysis",
                "Synthesize multi-source response"
            ],
            "general_query": [
                "Understand the request",
                "Gather relevant information",
                "Apply reasoning for insights",
                "Provide helpful response"
            ]
        }

        return plans.get(intent, ["Analyze request", "Apply reasoning", "Provide response"])
    
    def list_tools(self) -> List[str]:
        """List available tools."""
        if not self.tools:
            return []
        return [tool.name for tool in self.tools]
    
    def _task_planning_node(self, state: AgentState) -> AgentState:
        """Core decision-making hub that evaluates current state and determines next action."""
        current_step = state.get("current_step", 0)
        plan = state.get("plan", [])
        planning_cycles = state.get("planning_cycles", 0)
        information_gathered = state.get("information_gathered", {})
        messages = state.get("messages", [])
        complexity_level = state.get("complexity_level", "moderate")

        # Prevent infinite loops
        if planning_cycles >= 10:
            return {**state, "next_action": "synthesize"}

        # For simple complexity, be more direct
        if complexity_level == "simple" and current_step >= 1:
            return {**state, "next_action": "synthesize"}

        # Analyze current progress
        progress_analysis = self._analyze_progress(state)

        # Determine next action based on progress and plan
        if current_step >= len(plan):
            next_action = "synthesize"
        elif progress_analysis["needs_web_search"]:
            next_action = "use_tool"
            # Add search tool call to messages
            search_query = progress_analysis["search_query"]
            tool_call = self._create_search_tool_call(search_query)
            messages.append(tool_call)
        elif progress_analysis.get("needs_token_analysis"):
            next_action = "use_tool"
            # Add token analysis tool call to messages
            token_call = self._create_token_analysis_tool_call(progress_analysis)
            messages.append(token_call)
        elif progress_analysis.get("needs_token_price"):
            next_action = "use_tool"
            token_price_call = self._create_token_price_tool_call(progress_analysis)
            messages.append(token_price_call)
        elif progress_analysis.get("needs_token_pairs"):
            next_action = "use_tool"
            token_pairs_call = self._create_token_pairs_tool_call(progress_analysis)
            messages.append(token_pairs_call)
        elif progress_analysis.get("needs_token_swaps"):
            next_action = "use_tool"
            token_swaps_call = self._create_token_swaps_tool_call(progress_analysis)
            messages.append(token_swaps_call)
        elif progress_analysis.get("needs_token_holders"):
            next_action = "use_tool"
            token_holders_call = self._create_token_holders_tool_call(progress_analysis)
            messages.append(token_holders_call)
        elif progress_analysis.get("needs_wallet_analysis"):
            next_action = "use_tool"
            wallet_call = self._create_wallet_analysis_tool_call(progress_analysis)
            messages.append(wallet_call)
        elif progress_analysis.get("needs_native_balance"):
            next_action = "use_tool"
            balance_call = self._create_native_balance_tool_call(progress_analysis)
            messages.append(balance_call)
        elif progress_analysis.get("needs_trading_history"):
            next_action = "use_tool"
            trading_call = self._create_trading_history_tool_call(progress_analysis)
            messages.append(trading_call)
        elif progress_analysis["needs_blockchain_data"]:
            next_action = "use_tool"
            # Add blockchain tool call to messages
            blockchain_call = self._create_blockchain_tool_call(progress_analysis)
            messages.append(blockchain_call)
        elif progress_analysis["needs_complex_reasoning"]:
            # Only apply complex reasoning for moderate/complex queries
            if complexity_level in ["moderate", "complex"]:
                next_action = "use_tool"
                # Add sequential thinking tool call
                thinking_call = self._create_thinking_tool_call(progress_analysis)
                messages.append(thinking_call)
            else:
                next_action = "synthesize"
        elif progress_analysis["sufficient_information"]:
            next_action = "synthesize"
        else:
            next_action = "use_tool"

        # Update reasoning history
        reasoning_history = state.get("reasoning_history", [])
        reasoning_history.append(f"Step {current_step + 1}: {progress_analysis['reasoning']}")

        return {
            **state,
            "next_action": next_action,
            "current_step": current_step + 1,
            "planning_cycles": planning_cycles + 1,
            "reasoning_history": reasoning_history,
            "messages": messages
        }

    def _analyze_progress(self, state: AgentState) -> Dict[str, Any]:
        """Analyze current progress and determine what's needed next."""
        user_input = state.get("input", "")
        intent = state.get("intent", "")
        information_gathered = state.get("information_gathered", {})
        current_step = state.get("current_step", 0)
        plan = state.get("plan", [])

        analysis = {
            "needs_web_search": False,
            "needs_blockchain_data": False,
            "needs_complex_reasoning": False,
            "sufficient_information": False,
            "reasoning": "",
            "search_query": "",
            "address_info": {},
            "thinking_prompt": ""
        }

        # Enhanced address analysis
        address_info = self._analyze_crypto_address(user_input)
        if address_info["found"]:
            analysis["address_info"] = address_info

        # Priority 1: Address analysis for address_analysis intent
        if intent == "address_analysis" and not information_gathered.get("blockchain_data"):
            analysis["needs_blockchain_data"] = True
            analysis["reasoning"] = f"Analyzing {address_info['blockchain']} address: {address_info['address']}"

        # Priority 2: Web search for research queries or mixed queries
        elif (("search" in user_input.lower() or "news" in user_input.lower() or
               "latest" in user_input.lower() or intent == "web_research") and
              not information_gathered.get("web_data")):
            analysis["needs_web_search"] = True
            analysis["search_query"] = self._extract_search_query(user_input)
            analysis["reasoning"] = f"Need to search for: {analysis['search_query']}"

        # Priority 3: Token analysis for Solana addresses (try token first, then wallet)
        elif (address_info["found"] and
              address_info["blockchain"] == "solana" and
              not information_gathered.get("token_metadata")):
            analysis["needs_token_analysis"] = True
            analysis["reasoning"] = f"Checking if Solana address is a token contract: {address_info['address']}"

        # Priority 4: Follow-up token analysis if we confirmed it's a token
        elif (information_gathered.get("is_token_contract") and
              not information_gathered.get("token_price")):
            analysis["needs_token_price"] = True
            analysis["reasoning"] = "Token contract confirmed, getting price and market data"

        # Priority 5: Token pairs analysis
        elif (information_gathered.get("token_price") and
              not information_gathered.get("token_pairs")):
            analysis["needs_token_pairs"] = True
            analysis["reasoning"] = "Getting trading pairs and liquidity data"

        # Priority 6: Token trading activity
        elif (information_gathered.get("token_pairs") and
              not information_gathered.get("token_swaps")):
            analysis["needs_token_swaps"] = True
            analysis["reasoning"] = "Analyzing recent trading activity"

        # Priority 7: Token holder analysis
        elif (information_gathered.get("token_swaps") and
              not information_gathered.get("token_holders")):
            analysis["needs_token_holders"] = True
            analysis["reasoning"] = "Analyzing holder distribution and concentration"

        # Priority 8: If token analysis failed, try comprehensive wallet analysis
        elif (information_gathered.get("token_metadata") and
              ("not found" in information_gathered["token_metadata"].lower() or
               "error" in information_gathered["token_metadata"].lower()) and
              not information_gathered.get("wallet_portfolio")):
            analysis["needs_wallet_analysis"] = True
            analysis["reasoning"] = "Token analysis failed, analyzing as wallet address"

        # Priority 9: Native balance check for wallets
        elif (information_gathered.get("wallet_portfolio") and
              not information_gathered.get("native_balance")):
            analysis["needs_native_balance"] = True
            analysis["reasoning"] = "Getting native balance verification"

        # Priority 10: Trading history for active wallets
        elif (information_gathered.get("native_balance") and
              not information_gathered.get("trading_history")):
            analysis["needs_trading_history"] = True
            analysis["reasoning"] = "Analyzing wallet trading patterns and history"

        # Priority 11: Blockchain data for other addresses or fallback
        elif address_info["found"] and not information_gathered.get("blockchain_data"):
            analysis["needs_blockchain_data"] = True
            analysis["reasoning"] = f"Need to analyze {address_info['blockchain']} address: {address_info['address']}"

        # Priority 3.5: If blockchain analysis failed or shows empty wallet, try web search for the address
        elif (address_info["found"] and
              information_gathered.get("blockchain_data") and
              (("error" in information_gathered["blockchain_data"].lower() or
                "bad request" in information_gathered["blockchain_data"].lower()) or
               ("0 token" in information_gathered["blockchain_data"].lower() and
                "0 nft" in information_gathered["blockchain_data"].lower())) and
              not information_gathered.get("web_data")):
            analysis["needs_web_search"] = True
            analysis["search_query"] = f"{address_info['address']} solana token contract meme coin"
            analysis["reasoning"] = f"Blockchain shows empty wallet, searching web to check if this is a token contract"

        # Priority 4: AUTOMATIC strategic reasoning when we have data
        elif ((information_gathered.get("web_data") or information_gathered.get("blockchain_data")) and
              not information_gathered.get("strategic_analysis")):
            analysis["needs_complex_reasoning"] = True
            analysis["thinking_prompt"] = self._create_thinking_prompt(state)
            analysis["reasoning"] = "Automatically applying strategic analysis to gathered data"

        # Priority 5: Check if we have sufficient information (be more strict for address analysis)
        elif (information_gathered.get("strategic_analysis") and
              (information_gathered.get("token_metadata") or
               information_gathered.get("wallet_portfolio") or
               information_gathered.get("blockchain_data")) and
              len(information_gathered) >= 3):
            analysis["sufficient_information"] = True
            analysis["reasoning"] = "Sufficient information gathered, ready to synthesize"

        # Default: Try to gather more relevant information
        else:
            if intent in ["web_research", "mixed_query"]:
                analysis["needs_web_search"] = True
                analysis["search_query"] = user_input
                analysis["reasoning"] = "Gathering web information for context"
            else:
                analysis["needs_web_search"] = True
                analysis["search_query"] = user_input
                analysis["reasoning"] = "Default to web search for more information"

        return analysis

    def _synthesis_node(self, state: AgentState) -> AgentState:
        """Combine all gathered information into a coherent final response."""
        information_gathered = state.get("information_gathered", {})
        reasoning_history = state.get("reasoning_history", [])
        user_input = state.get("input", "")
        messages = state.get("messages", [])

        # Extract information from tool outputs
        web_data = information_gathered.get("web_data", "")
        blockchain_data = information_gathered.get("blockchain_data", "")
        strategic_analysis = information_gathered.get("strategic_analysis", "")

        # Create synthesis prompt
        synthesis_prompt = f"""
Based on the user's request: "{user_input}"

I have gathered the following information:

{f"Web Research Data: {web_data}" if web_data else ""}
{f"Blockchain Analysis: {blockchain_data}" if blockchain_data else ""}
{f"Strategic Analysis: {strategic_analysis}" if strategic_analysis else ""}

Reasoning Process:
{chr(10).join(reasoning_history)}

Please provide a comprehensive, well-structured response that synthesizes all this information to fully address the user's request.
"""

        # Generate final response using the model
        try:
            response = self.model.invoke([HumanMessage(content=synthesis_prompt)])
            final_response = response.content
        except Exception as e:
            final_response = f"I've gathered information but encountered an error in synthesis: {e}. Here's what I found: {web_data} {blockchain_data} {strategic_analysis}"

        return {
            **state,
            "final_response": final_response,
            "next_action": "complete"
        }

    async def _enhanced_tool_node(self, state: AgentState) -> AgentState:
        """Enhanced tool node that executes tools and processes outputs."""
        # Use the standard ToolNode for execution
        tool_node = ToolNode(self.tools)

        # Execute tools asynchronously - ToolNode expects MessagesState format
        messages_state = {"messages": state.get("messages", [])}
        result = await tool_node.ainvoke(messages_state)

        # Update the state with new messages and process tool outputs
        updated_state = {
            **state,
            "messages": result.get("messages", state.get("messages", []))
        }

        # Process the tool outputs and update information_gathered
        final_state = self._process_tool_outputs(updated_state)

        return final_state

    def _route_from_router(self, state: AgentState) -> str:
        """Route based on execution mode determined by router."""
        execution_mode = state.get("execution_mode", "multi_step")
        if execution_mode == "direct":
            return "direct"
        else:
            return "multi_step"

    async def _direct_execution_node(self, state: AgentState) -> AgentState:
        """Execute simple queries directly without multi-step planning."""
        user_input = state.get("input", "")
        intent = state.get("intent", "")
        plan = state.get("plan", [])

        # Create appropriate tool call based on intent
        tool_call = self._create_direct_tool_call(user_input, intent, state)

        if tool_call:
            # Execute the tool directly
            messages = [tool_call]
            tool_node = ToolNode(self.tools)

            try:
                # Execute tool asynchronously
                messages_state = {"messages": messages}
                result = await tool_node.ainvoke(messages_state)

                # Extract and process the response from tool message
                tool_messages = result.get("messages", [])
                if tool_messages:
                    raw_content = tool_messages[-1].content
                    tool_name = getattr(tool_messages[-1], 'name', '')

                    # Process the content for better user experience
                    processed_content = self._process_tool_content(tool_name, raw_content)

                    # Create a user-friendly response
                    if intent == "calculation":
                        final_response = f"The result is: {processed_content}"
                    elif intent == "blockchain_analysis":
                        final_response = f"Blockchain Analysis Results:\n{processed_content}"
                    elif intent == "web_research":
                        final_response = f"Search Results:\n{processed_content}"
                    else:
                        final_response = processed_content
                else:
                    final_response = "No response from tool execution."

            except Exception as e:
                final_response = f"Error executing request: {e}"
        else:
            # Fallback for queries that don't need tools
            final_response = self._handle_simple_query_directly(user_input, intent)

        return {
            **state,
            "final_response": final_response,
            "next_action": "complete",
            "reasoning_history": state.get("reasoning_history", []) + ["Direct execution completed"]
        }

    def _create_direct_tool_call(self, user_input: str, intent: str, state: AgentState):
        """Create a direct tool call for simple queries."""
        from langchain_core.messages import AIMessage

        # Get address info if available
        address_info = self._analyze_crypto_address(user_input)

        if intent == "calculation":
            # Extract math operation
            math_match = re.search(r'(\d+)\s*([\+\-\*\/])\s*(\d+)', user_input)
            if math_match:
                a, op, b = math_match.groups()
                if op == '+':
                    return AIMessage(content="", tool_calls=[{
                        "name": "add", "args": {"a": int(a), "b": int(b)}, "id": "calc_1"
                    }])
                elif op == '*':
                    return AIMessage(content="", tool_calls=[{
                        "name": "multiply", "args": {"a": int(a), "b": int(b)}, "id": "calc_1"
                    }])
                elif op == '/':
                    return AIMessage(content="", tool_calls=[{
                        "name": "divide", "args": {"a": int(a), "b": int(b)}, "id": "calc_1"
                    }])

        elif intent == "blockchain_analysis" and address_info["found"]:
            blockchain = address_info.get("blockchain", "ethereum")
            address = address_info.get("address", "")

            if blockchain == "ethereum":
                # Choose appropriate tool based on query context
                if "nft" in user_input.lower():
                    tool_name = "evm_getwalletnfts"
                else:
                    tool_name = "evm_getwallettokenbalancesprice"

                return AIMessage(content="", tool_calls=[{
                    "name": tool_name, "args": {"address": address}, "id": "blockchain_1"
                }])

            elif blockchain == "solana":
                return AIMessage(content="", tool_calls=[{
                    "name": "solana_getportfolio",
                    "args": {"address": address, "network": "mainnet"},
                    "id": "blockchain_1"
                }])

        elif intent == "web_research":
            # Extract search query
            search_query = self._extract_search_query(user_input)
            tool_name = "tavily_search" if "tavily_search" in [t.name for t in self.tools] else "search"

            return AIMessage(content="", tool_calls=[{
                "name": tool_name, "args": {"query": search_query}, "id": "search_1"
            }])

        return None

    def _handle_simple_query_directly(self, user_input: str, intent: str) -> str:
        """Handle simple queries that don't need tool execution."""
        if intent == "general_query":
            return f"I understand you're asking about: {user_input}. Could you provide more specific details about what you'd like me to help you with?"
        else:
            return f"I'm ready to help with your {intent} request. Please provide more specific details if needed."

    def _route_from_planner(self, state: AgentState) -> str:
        """Route based on planner's next_action decision."""
        action = state.get("next_action", "")
        if action == "use_tool":
            return "tools"
        elif action == "think":
            return "tools"  # Sequential thinking is a tool in our setup
        elif action == "synthesize":
            return "synthesizer"
        else:
            return "synthesizer"  # Default to completion

    def _extract_search_query(self, user_input: str) -> str:
        """Extract search query from user input."""
        # Look for specific search terms
        search_patterns = [
            r"search for (.+?)(?:\s+and|\s+then|$)",
            r"find (.+?)(?:\s+and|\s+then|$)",
            r"latest (.+?)(?:\s+and|\s+then|$)",
            r"news about (.+?)(?:\s+and|\s+then|$)"
        ]

        for pattern in search_patterns:
            match = re.search(pattern, user_input.lower())
            if match:
                return match.group(1).strip()

        # Default to the full input if no specific pattern found
        return user_input

    def _analyze_crypto_address(self, text: str) -> dict:
        """Comprehensive cryptocurrency address analysis with intelligent blockchain detection."""
        address_info = {
            "found": False,
            "address": "",
            "blockchain": "",
            "address_type": "",
            "confidence": 0.0,
            "possible_blockchains": []
        }

        # Extract potential address from text
        potential_address = text.strip()

        # Enhanced address patterns with priority-based blockchain identification
        patterns = [
            {
                "blockchain": "ethereum",
                "pattern": r"(0x[a-fA-F0-9]{40})",
                "description": "Ethereum/EVM-compatible address",
                "priority": 1
            },
            {
                "blockchain": "bitcoin_segwit",
                "pattern": r"(bc1[a-z0-9]{39,59})",
                "description": "Bitcoin Bech32 address",
                "priority": 1
            },
            {
                "blockchain": "bitcoin",
                "pattern": r"([13][a-km-zA-HJ-NP-Z1-9]{25,34})",
                "description": "Bitcoin Legacy address",
                "priority": 2
            },
            {
                "blockchain": "solana",
                "pattern": r"([1-9A-HJ-NP-Za-km-z]{32,44})",
                "description": "Solana address (Base58, 32-44 chars)",
                "priority": 3
            },
            {
                "blockchain": "cardano",
                "pattern": r"(addr1[a-z0-9]{98})",
                "description": "Cardano address",
                "priority": 4
            }
        ]

        # Check each pattern and collect all possible matches
        possible_matches = []

        for pattern_info in patterns:
            match = re.search(pattern_info["pattern"], potential_address)
            if match:
                address = match.group(1)

                # Additional validation
                confidence = 0.5
                if pattern_info["blockchain"] == "ethereum":
                    if any(c.isupper() for c in address[2:]) and any(c.islower() for c in address[2:]):
                        confidence = 0.95  # Checksummed
                    else:
                        confidence = 0.9

                elif pattern_info["blockchain"] == "solana":
                    # Solana addresses are typically 32-44 characters, Base58
                    if 32 <= len(address) <= 44:
                        # Additional Solana-specific validation
                        if not any(c in address for c in '0OIl'):  # Base58 excludes these
                            confidence = 0.85
                        else:
                            confidence = 0.6
                    else:
                        confidence = 0.4

                elif pattern_info["blockchain"] in ["bitcoin", "bitcoin_segwit"]:
                    confidence = 0.8

                else:
                    confidence = 0.7

                possible_matches.append({
                    "blockchain": pattern_info["blockchain"],
                    "address": address,
                    "confidence": confidence,
                    "priority": pattern_info["priority"],
                    "description": pattern_info["description"]
                })

        if possible_matches:
            # Sort by confidence first, then by priority
            possible_matches.sort(key=lambda x: (x["confidence"], -x["priority"]), reverse=True)

            # Use the best match
            best_match = possible_matches[0]

            address_info.update({
                "found": True,
                "address": best_match["address"],
                "blockchain": best_match["blockchain"],
                "address_type": best_match["blockchain"],
                "confidence": best_match["confidence"],
                "description": best_match["description"],
                "possible_blockchains": [m["blockchain"] for m in possible_matches]
            })

        return address_info

    def _contains_crypto_address(self, text: str) -> bool:
        """Check if text contains cryptocurrency addresses."""
        return self._analyze_crypto_address(text)["found"]

    def _extract_crypto_address(self, text: str) -> str:
        """Extract cryptocurrency address from text."""
        return self._analyze_crypto_address(text)["address"]

    def _create_search_tool_call(self, query: str):
        """Create a tool call for web search."""
        from langchain_core.messages import AIMessage

        # Prefer Tavily, fallback to DuckDuckGo
        tool_name = "tavily_search" if "tavily_search" in self.list_tools() else "search"

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": {"query": query},
                "id": f"search_{hash(query) % 10000}"
            }]
        )

    def _create_blockchain_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for blockchain analysis with fallback support."""
        from langchain_core.messages import AIMessage

        # Get address info from analysis
        address_info = analysis.get("address_info", {})
        if not address_info:
            # Fallback to old method
            wallet_address = analysis.get("wallet_address", "")
            if wallet_address.startswith("0x"):
                blockchain = "ethereum"
                address = wallet_address
                possible_blockchains = ["ethereum"]
            else:
                blockchain = "solana"
                address = wallet_address
                possible_blockchains = ["solana"]
        else:
            blockchain = address_info.get("blockchain", "ethereum")
            address = address_info.get("address", "")
            possible_blockchains = address_info.get("possible_blockchains", [blockchain])

        # Try the primary blockchain first
        tool_name, args = self._get_blockchain_tool_and_args(blockchain, address)

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": args,
                "id": f"blockchain_{hash(address) % 10000}"
            }]
        )

    def _create_token_analysis_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for token analysis (Solana token metadata)."""
        from langchain_core.messages import AIMessage

        # Get address info from analysis
        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")

        # Start with token metadata to determine if it's a token contract
        tool_name = "solana_gettokenmetadata"
        args = {"address": address, "network": "mainnet"}

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": args,
                "id": f"token_metadata_{hash(address) % 10000}"
            }]
        )

    def _create_token_price_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for token price analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")

        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_gettokenprice",
                "args": {"address": address, "network": "mainnet"},
                "id": f"token_price_{hash(address) % 10000}"
            }]
        )

    def _create_token_pairs_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for token pairs analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")

        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_gettokenpairs",
                "args": {"address": address, "network": "mainnet", "limit": 10},
                "id": f"token_pairs_{hash(address) % 10000}"
            }]
        )

    def _create_token_swaps_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for token swaps analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")

        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_getswapsbytokenaddress",
                "args": {
                    "address": address,
                    "network": "mainnet",
                    "limit": 10,
                    "transactionTypes": "buy,sell"
                },
                "id": f"token_swaps_{hash(address) % 10000}"
            }]
        )

    def _create_token_holders_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for token holders analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")

        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_gettokenholders",
                "args": {"address": address, "network": "mainnet"},
                "id": f"token_holders_{hash(address) % 10000}"
            }]
        )

    def _create_wallet_analysis_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for comprehensive wallet portfolio analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")
        blockchain = address_info.get("blockchain", "solana")

        if blockchain == "solana":
            tool_name = "solana_getportfolio"
            args = {
                "address": address,
                "network": "mainnet",
                "excludeSpam": True,
                "mediaItems": True,
                "nftMetadata": True
            }
        else:
            # For Ethereum, use wallet token balances
            tool_name = "evm_getwallettokenbalancesprice"
            args = {"address": address}

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": args,
                "id": f"wallet_portfolio_{hash(address) % 10000}"
            }]
        )

    def _create_native_balance_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for native balance verification."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")
        blockchain = address_info.get("blockchain", "solana")

        if blockchain == "solana":
            tool_name = "solana_balance"
            args = {"address": address, "network": "mainnet"}
        else:
            # For Ethereum, native balance is included in portfolio call
            tool_name = "evm_getnativebalancesforaddresses"
            args = {"wallet_addresses": [address]}

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": args,
                "id": f"native_balance_{hash(address) % 10000}"
            }]
        )

    def _create_trading_history_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for trading history analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")
        blockchain = address_info.get("blockchain", "solana")

        if blockchain == "solana":
            tool_name = "solana_getswapsbywalletaddress"
            args = {
                "address": address,
                "network": "mainnet",
                "limit": 10
            }
        else:
            # For Ethereum, use wallet history
            tool_name = "evm_getwallethistory"
            args = {"address": address, "limit": 10}

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": args,
                "id": f"trading_history_{hash(address) % 10000}"
            }]
        )

    def _get_blockchain_tool_and_args(self, blockchain: str, address: str) -> tuple:
        """Get the appropriate tool name and arguments for a blockchain."""
        if blockchain == "ethereum":
            return "evm_getwallettokenbalancesprice", {"address": address}
        elif blockchain == "solana":
            return "solana_getportfolio", {"address": address, "network": "mainnet"}
        elif blockchain in ["bitcoin", "bitcoin_segwit"]:
            # For Bitcoin, we might need to use a different approach or web search
            return "evm_getwallettokenbalancesprice", {"address": address}  # Will fail, triggering web search
        else:
            # Default to Ethereum, will fail and trigger alternatives
            return "evm_getwallettokenbalancesprice", {"address": address}

    def _create_thinking_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for sequential thinking."""
        from langchain_core.messages import AIMessage

        thinking_prompt = analysis.get("thinking_prompt", "Analyze the gathered data and provide strategic insights.")

        return AIMessage(
            content="",
            tool_calls=[{
                "name": "sequentialthinking",
                "args": {
                    "thought": thinking_prompt,
                    "nextThoughtNeeded": True,
                    "thoughtNumber": 1,
                    "totalThoughts": 3
                },
                "id": f"thinking_{hash(thinking_prompt) % 10000}"
            }]
        )

    def _create_thinking_prompt(self, state: AgentState) -> str:
        """Create a prompt for sequential thinking based on current state."""
        user_input = state.get("input", "")
        intent = state.get("intent", "")
        information_gathered = state.get("information_gathered", {})

        # Create context-aware thinking prompt
        if intent == "address_analysis":
            prompt = f"I need to analyze the blockchain address: {user_input}\n\n"
        else:
            prompt = f"User request: {user_input}\n\n"

        prompt += "Available data to analyze:\n"

        if information_gathered.get("web_data"):
            web_data = information_gathered['web_data'][:800]
            prompt += f"Web research findings: {web_data}...\n\n"

        if information_gathered.get("blockchain_data"):
            blockchain_data = information_gathered['blockchain_data'][:800]
            prompt += f"Blockchain analysis results: {blockchain_data}...\n\n"

        # Specific analysis request based on intent
        if intent == "address_analysis":
            prompt += "Please provide strategic analysis including:\n"
            prompt += "1. What type of address this is (wallet, contract, etc.)\n"
            prompt += "2. Activity patterns and risk assessment\n"
            prompt += "3. Portfolio composition and recommendations\n"
            prompt += "4. Any notable insights or red flags"
        else:
            prompt += "Please analyze this information step by step and provide strategic insights, recommendations, or conclusions."

        return prompt

    async def invoke(self, message: str, config: RunnableConfig = None) -> Dict[str, Any]:
        """Invoke the enhanced agent with a message."""
        if not self.graph:
            raise RuntimeError("Agent not initialized. Call initialize() first.")

        if config is None:
            config = RunnableConfig(
                recursion_limit=50,  # Higher limit for complex reasoning
                thread_id="enhanced_agent"
            )

        # Analyze the query to determine if we should use React agent
        address_info = self._analyze_crypto_address(message)
        intent = self._determine_intent(message, address_info)

        # For address analysis, use the React agent for proper MCP tool execution
        if intent == "address_analysis" and hasattr(self, 'react_agent'):
            try:
                react_result = await self._process_with_react_agent(message, address_info)
                return {
                    "input": message,
                    "intent": intent,
                    "final_response": react_result,
                    "execution_mode": "react_agent",
                    "address_info": address_info
                }
            except Exception as e:
                print(f"React agent failed: {e}, falling back to custom graph")

        # Create initial state for custom graph
        initial_state = {
            "input": message,
            "intent": "",
            "final_response": "",
            "plan": [],
            "current_step": 0,
            "reasoning_history": [],
            "messages": [],
            "tool_outputs": [],
            "information_gathered": {},
            "next_action": "",
            "confidence_level": 0.0,
            "planning_cycles": 0
        }

        # Run the graph
        result = await self.graph.ainvoke(initial_state, config=config)

        return result

    async def _process_with_react_agent(self, user_input: str, address_info: dict) -> str:
        """Process address analysis using the React agent for proper MCP tool execution."""
        blockchain = address_info.get("blockchain", "unknown")
        address = address_info.get("address", "")

        if blockchain == "solana":
            prompt = f"""
            Analyze the Solana address: {address}

            FOLLOW THIS EXACT SYSTEMATIC PROCESS:

            STEP 1: TOKEN IDENTIFICATION
            - Call solana_gettokenmetadata with network="mainnet" and address="{address}"
            - If successful (not 404), this is a TOKEN CONTRACT - proceed to TOKEN ANALYSIS
            - If 404 error, this is a WALLET ADDRESS - proceed to WALLET ANALYSIS

            IF TOKEN CONTRACT - FOLLOW 5-STEP TOKEN RESEARCH METHODOLOGY:

            STEP 2: TOKEN PRICE & MARKET DATA
            - Call solana_gettokenprice with network="mainnet" and address="{address}"
            - Extract current USD price, 24h change, trading pair info

            STEP 3: TRADING PAIRS & LIQUIDITY ANALYSIS
            - Call solana_gettokenpairs with network="mainnet", address="{address}", limit=10
            - Analyze all trading pairs, exchanges (Raydium, Orca), liquidity amounts

            STEP 4: TRADING ACTIVITY ANALYSIS
            - Call solana_getswapsbytokenaddress with network="mainnet", address="{address}", limit=10, transactionTypes="buy,sell"
            - Analyze recent transactions, buy/sell patterns, transaction sizes

            STEP 5: HOLDER DISTRIBUTION ANALYSIS
            - Call solana_gettokenholders with network="mainnet" and address="{address}"
            - Analyze total holders, concentration risk, holder trends

            IF WALLET ADDRESS - FOLLOW 9-STEP WALLET INVESTIGATION METHODOLOGY:

            STEP 2: PORTFOLIO ANALYSIS
            - Call solana_getportfolio with network="mainnet", address="{address}", excludeSpam=true, mediaItems=true, nftMetadata=true
            - Extract all token holdings, NFTs, detailed metadata

            STEP 3: NATIVE BALANCE VERIFICATION
            - Call solana_balance with network="mainnet" and address="{address}"
            - Verify SOL balance in lamports and convert to SOL

            STEP 4: TRADING HISTORY ANALYSIS
            - Call solana_getswapsbywalletaddress with network="mainnet", address="{address}", limit=10
            - Analyze trading patterns, exchanges used, profit/loss, strategy

            FINAL ANALYSIS:
            - Provide comprehensive analysis with risk assessment
            - Include actionable insights and recommendations
            - For tokens: market cap, liquidity risk, concentration risk, trading instructions
            - For wallets: portfolio composition, trading patterns, wallet characteristics

            CRITICAL: Use the actual MCP tools with exact parameters shown. Do not skip steps.
            """
        elif blockchain == "ethereum":
            prompt = f"""
            Analyze the Ethereum address: {address}

            FOLLOW THIS SYSTEMATIC ETHEREUM ANALYSIS PROCESS:

            STEP 1: WALLET TOKEN ANALYSIS
            - Call evm_getwallettokenbalancesprice with address="{address}"
            - Extract all ERC20 token holdings, balances, USD values

            STEP 2: NFT COLLECTION ANALYSIS
            - Call evm_getwalletnfts with address="{address}", limit=20
            - Analyze NFT holdings, collections, floor prices

            STEP 3: TRANSACTION HISTORY ANALYSIS
            - Call evm_getwallethistory with address="{address}", limit=10
            - Analyze transaction patterns, DeFi activity, trading behavior

            STEP 4: COMPREHENSIVE ANALYSIS
            - Provide detailed portfolio breakdown
            - Assess risk factors and wallet characteristics
            - Include actionable insights and recommendations

            Use the actual MCP tools with exact parameters shown.
            """
        else:
            prompt = f"Analyze the {blockchain} address: {address} using appropriate tools."

        # Configure the React agent
        config = RunnableConfig(recursion_limit=30, thread_id=1)

        # Execute with the React agent
        result = await self.react_agent.ainvoke(
            {"messages": [HumanMessage(content=prompt)]},
            config=config
        )

        # Extract the final response
        if result and "messages" in result:
            final_message = result["messages"][-1]
            return final_message.content
        else:
            return "No response from React agent"

    async def stream(self, message: str, config: RunnableConfig = None):
        """Stream the enhanced agent response."""
        if not self.graph:
            raise RuntimeError("Agent not initialized. Call initialize() first.")

        if config is None:
            config = RunnableConfig(
                recursion_limit=50,
                thread_id="enhanced_agent"
            )

        # Create initial state
        initial_state = {
            "input": message,
            "intent": "",
            "final_response": "",
            "plan": [],
            "current_step": 0,
            "reasoning_history": [],
            "messages": [],
            "tool_outputs": [],
            "information_gathered": {},
            "next_action": "",
            "confidence_level": 0.0,
            "planning_cycles": 0
        }

        async for chunk in self.graph.astream(initial_state, config=config):
            yield chunk

    def _process_tool_outputs(self, state: AgentState) -> AgentState:
        """Process tool outputs and update information_gathered."""
        from langchain_core.messages import ToolMessage
        import json

        messages = state.get("messages", [])
        information_gathered = state.get("information_gathered", {})

        # Look for tool messages in the conversation
        for message in messages:
            if isinstance(message, ToolMessage):
                # Get tool name from the message
                tool_name = getattr(message, 'name', '')
                if not tool_name:
                    # Try to get from tool_call_id or other attributes
                    tool_call_id = getattr(message, 'tool_call_id', '')
                    if 'search' in tool_call_id or 'tavily' in tool_call_id:
                        tool_name = 'search'
                    elif 'blockchain' in tool_call_id:
                        tool_name = 'blockchain'
                    elif 'thinking' in tool_call_id:
                        tool_name = 'sequentialthinking'

                content = message.content

                # Process and summarize tool outputs for better user experience
                processed_content = self._process_tool_content(tool_name, content)

                # Categorize tool outputs based on content and tool name
                if any(search_tool in tool_name.lower() for search_tool in ['tavily_search', 'search', 'tavily']):
                    information_gathered["web_data"] = processed_content
                elif 'gettokenmetadata' in tool_name.lower():
                    information_gathered["token_metadata"] = processed_content
                    # If token metadata is successful, we need more token analysis
                    if 'error' not in content.lower() and 'not found' not in content.lower():
                        information_gathered["is_token_contract"] = True
                elif 'gettokenprice' in tool_name.lower():
                    information_gathered["token_price"] = processed_content
                elif 'gettokenpairs' in tool_name.lower():
                    information_gathered["token_pairs"] = processed_content
                elif 'getswapsbytokenaddress' in tool_name.lower():
                    information_gathered["token_swaps"] = processed_content
                elif 'gettokenholders' in tool_name.lower():
                    information_gathered["token_holders"] = processed_content
                elif 'getportfolio' in tool_name.lower():
                    information_gathered["wallet_portfolio"] = processed_content
                elif 'balance' in tool_name.lower() and 'native' not in tool_name.lower():
                    information_gathered["native_balance"] = processed_content
                elif 'getswapsbywalletaddress' in tool_name.lower() or 'getwallethistory' in tool_name.lower():
                    information_gathered["trading_history"] = processed_content
                elif any(blockchain_tool in tool_name.lower() for blockchain_tool in ['evm_', 'solana_', 'blockchain']):
                    information_gathered["blockchain_data"] = processed_content
                elif 'sequentialthinking' in tool_name.lower() or 'thinking' in tool_name.lower():
                    information_gathered["strategic_analysis"] = processed_content
                elif 'error' not in content.lower():
                    # If we can't categorize but it's not an error, try to infer from content
                    if any(keyword in content.lower() for keyword in ['balance', 'token', 'nft', 'wallet', 'address']):
                        information_gathered["blockchain_data"] = processed_content
                    elif any(keyword in content.lower() for keyword in ['search', 'news', 'article', 'website']):
                        information_gathered["web_data"] = processed_content

        return {**state, "information_gathered": information_gathered}

    def _process_tool_content(self, tool_name: str, content: str) -> str:
        """Process raw tool content into user-friendly summaries."""
        import json
        import re

        try:
            # Handle MCP tool responses that include API status
            if "API Response (Status:" in content:
                # Extract JSON from the response
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    json_content = json_match.group(0)
                    data = json.loads(json_content)
                else:
                    return content

            # Try to parse JSON content directly
            elif content.startswith('{') or content.startswith('['):
                data = json.loads(content)

            else:
                # Content is already text, return as-is
                return content

            # Process different types of data
            if any(blockchain_tool in tool_name.lower() for blockchain_tool in ['evm_', 'solana_', 'blockchain']):
                return self._summarize_blockchain_data(data)

            elif any(search_tool in tool_name.lower() for search_tool in ['tavily', 'search']):
                return self._summarize_search_data(data)

            else:
                return self._summarize_generic_data(data)

        except (json.JSONDecodeError, Exception) as e:
            # If parsing fails, try to extract useful info from text
            if any(blockchain_tool in tool_name.lower() for blockchain_tool in ['evm_', 'solana_', 'blockchain']):
                return self._extract_blockchain_summary_from_text(content)
            else:
                return content[:300] + "..." if len(content) > 300 else content

    def _summarize_blockchain_data(self, data) -> str:
        """Summarize blockchain data into user-friendly format."""
        if isinstance(data, dict):
            summary = []

            # Handle wallet balance data
            if 'result' in data and isinstance(data['result'], list):
                tokens = data['result']
                if tokens:
                    summary.append(f"Wallet contains {len(tokens)} token(s):")
                    for token in tokens[:5]:  # Show first 5 tokens
                        name = token.get('name', 'Unknown')
                        symbol = token.get('symbol', '')
                        balance = token.get('balance_formatted', token.get('balance', '0'))
                        usd_value = token.get('usd_value', 0)
                        summary.append(f"  • {name} ({symbol}): {balance} (${usd_value:.2f})")
                    if len(tokens) > 5:
                        summary.append(f"  ... and {len(tokens) - 5} more tokens")
                else:
                    summary.append("Wallet appears to be empty or contains no tokens")

            # Handle NFT data
            elif 'result' in data and 'total' in data:
                total = data.get('total', 0)
                if total > 0:
                    summary.append(f"Wallet contains {total} NFT(s)")
                    nfts = data.get('result', [])
                    for nft in nfts[:3]:  # Show first 3 NFTs
                        name = nft.get('name', 'Unknown NFT')
                        collection = nft.get('token_address', '')[:10] + '...'
                        summary.append(f"  • {name} (Collection: {collection})")
                else:
                    summary.append("No NFTs found in this wallet")

            # Handle Solana portfolio data
            elif 'tokens' in data or 'nfts' in data:
                if 'tokens' in data:
                    tokens = data['tokens']
                    summary.append(f"Solana wallet contains {len(tokens)} token(s)")
                if 'nfts' in data:
                    nfts = data['nfts']
                    summary.append(f"Contains {len(nfts)} NFT(s)")

            return '\n'.join(summary) if summary else str(data)[:200] + "..."

        return str(data)[:200] + "..."

    def _summarize_search_data(self, data) -> str:
        """Summarize search results into user-friendly format."""
        if isinstance(data, list):
            summary = [f"Found {len(data)} search result(s):"]
            for i, result in enumerate(data[:3], 1):  # Show first 3 results
                if isinstance(result, dict):
                    title = result.get('title', result.get('name', f'Result {i}'))
                    url = result.get('url', result.get('link', ''))
                    content = result.get('content', result.get('snippet', ''))[:100]
                    summary.append(f"{i}. {title}")
                    if content:
                        summary.append(f"   {content}...")
            return '\n'.join(summary)

        elif isinstance(data, dict):
            if 'results' in data:
                return self._summarize_search_data(data['results'])
            else:
                title = data.get('title', 'Search Result')
                content = data.get('content', str(data))[:200]
                return f"{title}: {content}..."

        return str(data)[:200] + "..."

    def _summarize_generic_data(self, data) -> str:
        """Summarize generic structured data."""
        if isinstance(data, dict):
            if len(data) <= 3:
                return str(data)
            else:
                keys = list(data.keys())[:3]
                summary = {k: data[k] for k in keys}
                return f"{summary}... (and {len(data) - 3} more fields)"
        elif isinstance(data, list):
            if len(data) <= 3:
                return str(data)
            else:
                return f"[{data[0]}, {data[1]}, {data[2]}, ... and {len(data) - 3} more items]"
        else:
            return str(data)[:200] + "..."

    def _extract_blockchain_summary_from_text(self, content: str) -> str:
        """Extract useful blockchain information from text content."""
        import re

        summary = []

        # Look for token information in the text
        token_matches = re.findall(r'"symbol":\s*"([^"]+)".*?"balance_formatted":\s*"([^"]+)".*?"usd_value":\s*([0-9.]+)', content, re.DOTALL)

        if token_matches:
            summary.append("Wallet Token Holdings:")
            total_value = 0
            for symbol, balance, usd_value in token_matches:
                usd_val = float(usd_value)
                total_value += usd_val
                if usd_val > 0.01:  # Only show tokens with meaningful value
                    summary.append(f"  • {symbol}: {balance} (${usd_val:.2f})")

            summary.append(f"\nTotal Portfolio Value: ${total_value:.2f}")

        # Look for NFT information
        nft_matches = re.findall(r'"total":\s*(\d+)', content)
        if nft_matches:
            total_nfts = nft_matches[0]
            summary.append(f"NFTs: {total_nfts} total")

        return '\n'.join(summary) if summary else "Blockchain data processed successfully"

    async def cleanup(self):
        """Cleanup resources."""
        if self.client:
            print("✅ MCP client cleaned up")

# Utility function for easy agent creation
async def create_enhanced_agent(server_configs: Dict[str, Dict[str, Any]] = None) -> EnhancedLangGraphMCPAgent:
    """Create and initialize an Enhanced LangGraph MCP agent."""
    agent = EnhancedLangGraphMCPAgent()
    await agent.initialize(server_configs)
    return agent

# Example usage
async def main():
    """Example usage of the Enhanced LangGraph MCP agent."""
    agent = None
    try:
        # Create and initialize the enhanced agent
        agent = await create_enhanced_agent()

        # List available tools
        print("\n📋 Available tools:")
        for tool in agent.list_tools():
            print(f"  - {tool}")

        # Example complex multi-step query
        complex_query = """Search for recent Bitcoin news, analyze wallet ******************************************,
        and use sequential thinking to provide investment recommendations based on the market context."""

        print(f"\n🤖 Running complex query:")
        print(f"Query: {complex_query}")

        response = await agent.invoke(complex_query)

        print(f"\n📊 Final Response:")
        print(response.get("final_response", "No response generated"))

        print(f"\n🧠 Reasoning Process:")
        for step in response.get("reasoning_history", []):
            print(f"  - {step}")

    except Exception as e:
        print(f"❌ Error: {e}")

    finally:
        if agent:
            await agent.cleanup()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
