#!/usr/bin/env python3
"""
Example usage of LangGraph MCP Agent
This script demonstrates various ways to use the agent with MCP tools.
"""

import asyncio
import os
from agent import create_agent, LangGraphMCPAgent
from langchain_core.runnables import RunnableConfig

async def basic_example():
    """Basic example of using the agent."""
    print("🚀 Basic Example - Simple Math and Text Operations")
    print("=" * 60)
    
    agent = None
    try:
        # Create agent
        agent = await create_agent()
        
        # Simple math
        response = await agent.invoke("What's 25 * 4 + 100?")
        print(f"Math result: {response['messages'][-1].content}")
        
        # Text processing
        response = await agent.invoke("Convert 'hello world' to uppercase")
        print(f"Text result: {response['messages'][-1].content}")
        
    finally:
        if agent:
            await agent.cleanup()

async def crypto_example():
    """Example focused on crypto-related operations."""
    print("\n💰 Crypto Example - Address Analysis and Hashing")
    print("=" * 60)
    
    agent = None
    try:
        agent = await create_agent()
        
        # Analyze crypto addresses
        addresses = [
            "******************************************",  # Ethereum
            "******************************************",      # Bitcoin
            "DJvE3EuZHwEzp2FwjKjLVkCXUj5srjqiDM"              # Potential other
        ]
        
        for addr in addresses:
            response = await agent.invoke(f"Analyze this crypto address: {addr}")
            print(f"Address {addr[:20]}...: {response['messages'][-1].content}")
        
        # Hash operations
        response = await agent.invoke("Hash the text 'my-secret-key' using SHA256")
        print(f"Hash result: {response['messages'][-1].content}")
        
    finally:
        if agent:
            await agent.cleanup()

async def json_example():
    """Example working with JSON data."""
    print("\n📄 JSON Example - Validation and Formatting")
    print("=" * 60)
    
    agent = None
    try:
        agent = await create_agent()
        
        # Valid JSON
        json_data = '{"name": "Bitcoin", "symbol": "BTC", "price": 45000}'
        response = await agent.invoke(f"Validate and format this JSON: {json_data}")
        print(f"Valid JSON result: {response['messages'][-1].content}")
        
        # Invalid JSON
        invalid_json = '{"name": "Bitcoin", "symbol": "BTC", "price": 45000'  # Missing closing brace
        response = await agent.invoke(f"Validate this JSON: {invalid_json}")
        print(f"Invalid JSON result: {response['messages'][-1].content}")
        
    finally:
        if agent:
            await agent.cleanup()

async def streaming_example():
    """Example of streaming responses."""
    print("\n🌊 Streaming Example - Real-time Response")
    print("=" * 60)
    
    agent = None
    try:
        agent = await create_agent()
        
        print("User: Calculate 123 * 456 and then encode the result in base64")
        print("Agent: ", end="", flush=True)
        
        async for chunk in agent.stream("Calculate 123 * 456 and then encode the result in base64"):
            if "messages" in chunk and chunk["messages"]:
                last_message = chunk["messages"][-1]
                if hasattr(last_message, 'content') and last_message.content:
                    print(last_message.content, end="", flush=True)
        
        print()  # New line after streaming
        
    finally:
        if agent:
            await agent.cleanup()

async def custom_server_example():
    """Example with custom MCP server configuration."""
    print("\n⚙️  Custom Server Example - Multiple MCP Servers")
    print("=" * 60)
    
    # You can configure multiple MCP servers
    current_dir = os.path.dirname(os.path.abspath(__file__))
    custom_config = {
        "utility_server": {
            "command": "python",
            "args": [os.path.join(current_dir, "simple_mcp_server.py")],
            "transport": "stdio"
        }
        # Add more servers here if you have them
        # "weather_server": {
        #     "url": "http://localhost:8000/mcp",
        #     "transport": "streamable_http"
        # }
    }
    
    agent = None
    try:
        agent = await create_agent(custom_config)
        
        print(f"Available tools: {', '.join(agent.list_tools())}")
        
        response = await agent.invoke("List all available tools and their purposes")
        print(f"Tools overview: {response['messages'][-1].content}")
        
    finally:
        if agent:
            await agent.cleanup()

async def interactive_example():
    """Interactive example where user can input queries."""
    print("\n💬 Interactive Example - Chat with the Agent")
    print("=" * 60)
    print("Type 'quit' to exit")
    
    agent = None
    try:
        agent = await create_agent()
        config = RunnableConfig(recursion_limit=30, thread_id="interactive")
        
        while True:
            user_input = input("\nYou: ").strip()
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            if not user_input:
                continue
            
            try:
                response = await agent.invoke(user_input, config)
                print(f"Agent: {response['messages'][-1].content}")
            except Exception as e:
                print(f"Error: {e}")
    
    finally:
        if agent:
            await agent.cleanup()

async def main():
    """Run all examples."""
    print("🎯 LangGraph MCP Agent Examples")
    print("=" * 60)
    
    # Check if environment is set up
    if not os.getenv("OPENROUTER_API_KEY"):
        print("❌ Error: OPENROUTER_API_KEY not found in environment variables")
        print("Please make sure your .env file is configured correctly.")
        return
    
    try:
        await basic_example()
        await crypto_example()
        await json_example()
        await streaming_example()
        await custom_server_example()
        
        # Uncomment to run interactive mode
        # await interactive_example()
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
