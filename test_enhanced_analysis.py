#!/usr/bin/env python3
"""
Test the enhanced React agent analysis capabilities.
"""

import asyncio
import sys
from enhanced_agent import create_enhanced_agent
from langchain_core.runnables import RunnableConfig

async def test_comprehensive_analysis():
    """Test the enhanced analysis with the same addresses."""
    print("🧪 Testing Enhanced Comprehensive Analysis")
    print("=" * 50)
    
    try:
        # Initialize agent
        print("Initializing enhanced agent...")
        agent = await create_enhanced_agent()
        print("✅ Agent initialized successfully\n")
        
        # Test the same addresses that were giving short responses
        test_addresses = [
            "HujryLm7hPMSr1mh6W2mtoLFttZbTnarq9hkXxqimjQL",  # Wallet that should give detailed analysis
            "EUxS8Kqvbe8zDr8bG1RYtgjVNzEhMTG6B6H3WwrCHfwA"   # Token that should give detailed analysis
        ]
        
        for i, address in enumerate(test_addresses, 1):
            print(f"🎯 Test {i}: Analyzing {address}")
            print("-" * 60)
            
            config = RunnableConfig(recursion_limit=50, thread_id=f"test_analysis_{i}")
            
            try:
                result = await agent.invoke(address, execution_profile="thorough", config=config)
                
                response = result.get('final_response', 'No response')
                print(f"✅ Analysis completed!")
                print(f"Response length: {len(response)} characters")
                print(f"Intent: {result.get('intent', 'unknown')}")
                print(f"Mode: {result.get('execution_mode', 'unknown')}")
                print(f"Reflection depth: {result.get('reflection_depth', 0)}")
                
                # Show first 500 characters to verify quality
                print(f"\nFirst 500 characters of response:")
                print("-" * 40)
                print(response[:500])
                print("-" * 40)
                
                # Check for key indicators of comprehensive analysis
                quality_indicators = [
                    "USD" in response,
                    "%" in response,
                    "holders" in response.lower(),
                    "trading" in response.lower(),
                    "analysis" in response.lower(),
                    len(response) > 1000  # Should be substantial
                ]
                
                quality_score = sum(quality_indicators)
                print(f"Quality Score: {quality_score}/6")
                
                if quality_score >= 4:
                    print("✅ High quality comprehensive analysis detected!")
                else:
                    print("⚠️  Analysis may need improvement")
                
                reasoning = result.get('reasoning_history', [])
                if reasoning:
                    print(f"Reasoning steps: {len(reasoning)}")
                    for step in reasoning[:3]:  # Show first 3 steps
                        print(f"  • {step}")
                
            except Exception as e:
                print(f"❌ Error analyzing {address}: {e}")
                import traceback
                traceback.print_exc()
            
            print("\n" + "="*60 + "\n")
        
        await agent.cleanup()
        print("✅ All tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the comprehensive analysis test."""
    success = await test_comprehensive_analysis()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
