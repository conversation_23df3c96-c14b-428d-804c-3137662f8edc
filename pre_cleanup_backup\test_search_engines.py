#!/usr/bin/env python3
"""
Test script to verify all search engines are working properly.
Tests Tavily (primary) and DuckDuckGo (backup) search capabilities.
"""

import asyncio
import os
from agent import create_agent

async def test_tavily_search():
    """Test Tavily search functionality."""
    print("🔍 Testing Tavily Search")
    print("=" * 40)
    
    agent = None
    try:
        agent = await create_agent()
        
        query = "Use tavily_search to find recent news about Bitcoin price"
        print(f"Query: {query}")
        
        response = await agent.invoke(query)
        print(f"Response: {response['messages'][-1].content[:200]}...")
        print("✅ Tavily search working")
        
    except Exception as e:
        print(f"❌ Tavily search failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def test_duckduckgo_search():
    """Test DuckDuckGo search functionality."""
    print("\n🦆 Testing DuckDuckGo Search")
    print("=" * 40)
    
    agent = None
    try:
        agent = await create_agent()
        
        query = "Use DuckDuckGo search to find information about Ethereum"
        print(f"Query: {query}")
        
        response = await agent.invoke(query)
        print(f"Response: {response['messages'][-1].content[:200]}...")
        print("✅ DuckDuckGo search working")
        
    except Exception as e:
        print(f"❌ DuckDuckGo search failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def test_content_fetching():
    """Test content fetching capabilities."""
    print("\n📄 Testing Content Fetching")
    print("=" * 40)
    
    agent = None
    try:
        agent = await create_agent()
        
        query = "Use fetch_content to get information from https://coinmarketcap.com"
        print(f"Query: {query}")
        
        response = await agent.invoke(query)
        print(f"Response: {response['messages'][-1].content[:200]}...")
        print("✅ Content fetching working")
        
    except Exception as e:
        print(f"❌ Content fetching failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def test_combined_search():
    """Test using both search engines together."""
    print("\n🔄 Testing Combined Search")
    print("=" * 40)
    
    agent = None
    try:
        agent = await create_agent()
        
        query = """Compare results from both search engines: 
        1. Use Tavily to search for 'crypto market news today'
        2. Use DuckDuckGo to search for the same topic
        3. Summarize the differences in results"""
        
        print(f"Query: {query}")
        
        response = await agent.invoke(query)
        print(f"Response: {response['messages'][-1].content[:300]}...")
        print("✅ Combined search working")
        
    except Exception as e:
        print(f"❌ Combined search failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def test_search_with_blockchain_data():
    """Test combining search with blockchain data."""
    print("\n🌐 Testing Search + Blockchain Integration")
    print("=" * 40)
    
    agent = None
    try:
        agent = await create_agent()
        
        query = """Do a comprehensive analysis:
        1. Search for recent news about Ethereum price
        2. Get current NFTs for wallet ******************************************
        3. Use sequential thinking to analyze the market context"""
        
        print(f"Query: {query}")
        
        response = await agent.invoke(query)
        print(f"Response: {response['messages'][-1].content[:300]}...")
        print("✅ Search + Blockchain integration working")
        
    except Exception as e:
        print(f"❌ Search + Blockchain integration failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def test_tool_availability():
    """Test which search tools are available."""
    print("\n🔧 Testing Tool Availability")
    print("=" * 40)
    
    agent = None
    try:
        agent = await create_agent()
        
        tools = agent.list_tools()
        search_tools = [tool for tool in tools if 'search' in tool.lower() or 'fetch' in tool.lower() or 'tavily' in tool.lower()]
        
        print("Available search-related tools:")
        for tool in search_tools:
            print(f"  ✅ {tool}")
        
        print(f"\nTotal search tools: {len(search_tools)}")
        print(f"Total tools: {len(tools)}")
        
    except Exception as e:
        print(f"❌ Tool availability check failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def main():
    """Run all search engine tests."""
    print("🚀 Search Engine Test Suite")
    print("=" * 50)
    
    # Check environment
    if not os.getenv("OPENROUTER_API_KEY"):
        print("❌ Error: OPENROUTER_API_KEY not found")
        return
    
    if not os.getenv("TAVILY_API_KEY"):
        print("⚠️  Warning: TAVILY_API_KEY not found - Tavily tests may fail")
    
    try:
        await test_tool_availability()
        await test_tavily_search()
        await test_duckduckgo_search()
        await test_content_fetching()
        await test_combined_search()
        await test_search_with_blockchain_data()
        
        print("\n✅ All search engine tests completed!")
        print("\nTo use interactively: python cli.py")
        
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
    except Exception as e:
        print(f"❌ Error running tests: {e}")

if __name__ == "__main__":
    asyncio.run(main())
