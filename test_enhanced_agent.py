#!/usr/bin/env python3
"""
Test script for the Enhanced LangGraph MCP Agent
Demonstrates multi-step reasoning capabilities with complex queries.
"""

import asyncio
import os
from enhanced_agent import create_enhanced_agent

async def test_simple_query():
    """Test with a simple query to verify basic functionality."""
    print("🔧 Test 1: Simple Query")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_enhanced_agent()
        
        query = "What's 25 * 4 + 100?"
        print(f"Query: {query}")
        
        result = await agent.invoke(query)
        
        print(f"Intent: {result.get('intent')}")
        print(f"Reasoning: {result.get('reasoning_history')}")
        print(f"Response: {result.get('final_response')}")
        print("✅ Simple query test passed")
        
    except Exception as e:
        print(f"❌ Simple query test failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def test_web_research():
    """Test web research capabilities."""
    print("\n🌐 Test 2: Web Research")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_enhanced_agent()
        
        query = "Search for recent Bitcoin price predictions for 2025"
        print(f"Query: {query}")
        
        result = await agent.invoke(query)
        
        print(f"Intent: {result.get('intent')}")
        print(f"Planning Cycles: {result.get('planning_cycles')}")
        print(f"Information Gathered: {list(result.get('information_gathered', {}).keys())}")
        print(f"Response Length: {len(result.get('final_response', ''))}")
        print("✅ Web research test passed")
        
    except Exception as e:
        print(f"❌ Web research test failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def test_blockchain_analysis():
    """Test blockchain analysis capabilities."""
    print("\n⛓️  Test 3: Blockchain Analysis")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_enhanced_agent()
        
        query = "Analyze wallet ****************************************** and get its NFTs"
        print(f"Query: {query}")
        
        result = await agent.invoke(query)
        
        print(f"Intent: {result.get('intent')}")
        print(f"Planning Cycles: {result.get('planning_cycles')}")
        print(f"Information Gathered: {list(result.get('information_gathered', {}).keys())}")
        print(f"Response Length: {len(result.get('final_response', ''))}")
        print("✅ Blockchain analysis test passed")
        
    except Exception as e:
        print(f"❌ Blockchain analysis test failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def test_complex_multi_step():
    """Test complex multi-step reasoning."""
    print("\n🧠 Test 4: Complex Multi-Step Reasoning")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_enhanced_agent()
        
        query = """Search for Ethereum price news, analyze wallet ******************************************, 
        and use sequential thinking to provide investment recommendations"""
        
        print(f"Query: {query}")
        
        result = await agent.invoke(query)
        
        print(f"Intent: {result.get('intent')}")
        print(f"Confidence: {result.get('confidence_level')}")
        print(f"Planning Cycles: {result.get('planning_cycles')}")
        print(f"Initial Plan: {result.get('plan')}")
        
        print("\nReasoning Process:")
        for i, step in enumerate(result.get('reasoning_history', []), 1):
            print(f"  {i}. {step}")
        
        print(f"\nInformation Gathered: {list(result.get('information_gathered', {}).keys())}")
        print(f"Response Length: {len(result.get('final_response', ''))}")
        print("✅ Complex multi-step test passed")
        
    except Exception as e:
        print(f"❌ Complex multi-step test failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def test_strategic_thinking():
    """Test strategic thinking integration."""
    print("\n🎯 Test 5: Strategic Thinking Integration")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_enhanced_agent()
        
        query = """Use sequential thinking to analyze the best approach for crypto portfolio diversification 
        given current market conditions"""
        
        print(f"Query: {query}")
        
        result = await agent.invoke(query)
        
        print(f"Intent: {result.get('intent')}")
        print(f"Planning Cycles: {result.get('planning_cycles')}")
        
        # Check if sequential thinking was used
        info_gathered = result.get('information_gathered', {})
        if 'strategic_analysis' in info_gathered:
            print("✅ Sequential thinking was successfully integrated")
        else:
            print("⚠️  Sequential thinking may not have been used")
        
        print(f"Response Length: {len(result.get('final_response', ''))}")
        print("✅ Strategic thinking test passed")
        
    except Exception as e:
        print(f"❌ Strategic thinking test failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def test_error_handling():
    """Test error handling and graceful degradation."""
    print("\n🛡️  Test 6: Error Handling")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_enhanced_agent()
        
        # Test with an invalid wallet address
        query = "Analyze wallet invalid_address_123 and provide recommendations"
        print(f"Query: {query}")
        
        result = await agent.invoke(query)
        
        print(f"Intent: {result.get('intent')}")
        print(f"Planning Cycles: {result.get('planning_cycles')}")
        
        # Should still provide some response even with errors
        final_response = result.get('final_response', '')
        if final_response:
            print("✅ Error handling test passed - agent provided graceful response")
        else:
            print("⚠️  Error handling test - no response generated")
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def test_loop_prevention():
    """Test loop prevention mechanisms."""
    print("\n🔄 Test 7: Loop Prevention")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_enhanced_agent()
        
        # Test with a potentially confusing query
        query = "Search for search results about searching for Bitcoin"
        print(f"Query: {query}")
        
        result = await agent.invoke(query)
        
        planning_cycles = result.get('planning_cycles', 0)
        print(f"Planning Cycles: {planning_cycles}")
        
        if planning_cycles <= 10:
            print("✅ Loop prevention test passed - cycles within limit")
        else:
            print("⚠️  Loop prevention test - too many cycles")
        
    except Exception as e:
        print(f"❌ Loop prevention test failed: {e}")
    finally:
        if agent:
            await agent.cleanup()

async def main():
    """Run all enhanced agent tests."""
    print("🚀 Enhanced LangGraph MCP Agent Test Suite")
    print("=" * 60)
    
    # Check environment
    if not os.getenv("OPENROUTER_API_KEY"):
        print("❌ Error: OPENROUTER_API_KEY not found")
        return
    
    try:
        await test_simple_query()
        await test_web_research()
        await test_blockchain_analysis()
        await test_complex_multi_step()
        await test_strategic_thinking()
        await test_error_handling()
        await test_loop_prevention()
        
        print("\n✅ All enhanced agent tests completed!")
        print("\nThe enhanced agent demonstrates:")
        print("  • Multi-step reasoning with visible planning")
        print("  • Intelligent tool selection and sequencing")
        print("  • Information synthesis from multiple sources")
        print("  • Strategic analysis integration")
        print("  • Robust error handling and loop prevention")
        print("\nTo use interactively: python enhanced_cli.py")
        
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
    except Exception as e:
        print(f"❌ Error running tests: {e}")

if __name__ == "__main__":
    asyncio.run(main())
