#!/usr/bin/env python3
"""
Test the enhanced agent with the BIRDFLU token to compare quality.
"""

import asyncio
import sys
from enhanced_agent import create_enhanced_agent
from langchain_core.runnables import RunnableConfig

async def test_birdflu_token_analysis():
    """Test the enhanced agent with BIRDFLU token."""
    print("🧪 Testing BIRDFLU Token Analysis Quality")
    print("=" * 50)
    
    try:
        # Initialize agent
        print("Initializing enhanced agent...")
        agent = await create_enhanced_agent()
        print("✅ Agent initialized successfully\n")
        
        # Test the BIRDFLU token
        birdflu_address = "EUxS8Kqvbe8zDr8bG1RYtgjVNzEhMTG6B6H3WwrCHfwA"
        
        print(f"🎯 Testing BIRDFLU Token Analysis")
        print(f"Address: {birdflu_address}")
        print("-" * 60)
        
        config = RunnableConfig(recursion_limit=50, thread_id="birdflu_test")
        
        # Use thorough profile for maximum analysis depth
        result = await agent.invoke(birdflu_address, execution_profile="thorough", config=config)
        
        response = result.get('final_response', 'No response')
        
        print(f"✅ Analysis completed!")
        print(f"Response length: {len(response)} characters")
        print(f"Intent: {result.get('intent', 'unknown')}")
        print(f"Execution mode: {result.get('execution_mode', 'unknown')}")
        print(f"Reflection depth: {result.get('reflection_depth', 0)}")
        
        # Show reasoning process
        reasoning = result.get('reasoning_history', [])
        if reasoning:
            print(f"\n🧠 Dynamic Reasoning Process ({len(reasoning)} steps):")
            for i, step in enumerate(reasoning, 1):
                print(f"  {i}. {step}")
        
        # Show information gathered
        info_gathered = result.get('information_gathered', {})
        print(f"\n📊 Information Sources Gathered: {len(info_gathered)}")
        for source, data in info_gathered.items():
            print(f"  • {source}: {len(str(data))} characters")
        
        # Show the full response
        print(f"\n📄 COMPLETE BIRDFLU TOKEN ANALYSIS:")
        print("=" * 60)
        print(response)
        print("=" * 60)
        
        # Quality assessment against the excellent example
        quality_indicators = [
            "BIRDFLU" in response or "Bird Flu" in response,
            "$" in response or "USD" in response,
            "%" in response,
            "holders" in response.lower(),
            "trading" in response.lower() or "liquidity" in response.lower(),
            "risk" in response.lower(),
            "market cap" in response.lower() or "supply" in response.lower(),
            "price" in response.lower(),
            len(response) > 1000  # Should be substantial like the example
        ]
        
        quality_score = sum(quality_indicators)
        print(f"\n📊 QUALITY ASSESSMENT: {quality_score}/9")
        
        # Compare with the excellent example
        print(f"\n📈 COMPARISON WITH EXCELLENT EXAMPLE:")
        print(f"Target example length: ~4,500 characters")
        print(f"Our response length: {len(response)} characters")
        print(f"Target sections: 8 major sections")
        print(f"Target data points: 50+ specific metrics")
        print(f"Target quality indicators: 9/9")
        print(f"Our quality indicators: {quality_score}/9")
        
        if quality_score >= 8:
            print("🎉 EXCELLENT: Matches target quality!")
        elif quality_score >= 6:
            print("✅ GOOD: Close to target quality")
        elif quality_score >= 4:
            print("⚠️  FAIR: Needs improvement")
        else:
            print("❌ POOR: Significant improvement needed")
        
        # Specific improvement suggestions
        missing_indicators = []
        if "BIRDFLU" not in response and "Bird Flu" not in response:
            missing_indicators.append("Token name/symbol")
        if "$" not in response and "USD" not in response:
            missing_indicators.append("USD values")
        if "%" not in response:
            missing_indicators.append("Percentage metrics")
        if "holders" not in response.lower():
            missing_indicators.append("Holder analysis")
        if "trading" not in response.lower() and "liquidity" not in response.lower():
            missing_indicators.append("Trading/liquidity data")
        if "risk" not in response.lower():
            missing_indicators.append("Risk assessment")
        if "market cap" not in response.lower() and "supply" not in response.lower():
            missing_indicators.append("Market cap/supply data")
        if "price" not in response.lower():
            missing_indicators.append("Price information")
        if len(response) <= 1000:
            missing_indicators.append("Comprehensive length")
        
        if missing_indicators:
            print(f"\n🔧 IMPROVEMENT NEEDED:")
            for indicator in missing_indicators:
                print(f"  • Missing: {indicator}")
        
        await agent.cleanup()
        print("\n✅ BIRDFLU token analysis test completed!")
        return quality_score >= 6
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the BIRDFLU token analysis test."""
    success = await test_birdflu_token_analysis()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
