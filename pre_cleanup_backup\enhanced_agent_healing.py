#!/usr/bin/env python3
"""
Self-Healing Integration for Enhanced LangGraph MCP Agent
Integrates advanced self-healing mechanisms with the existing agent architecture.
"""

import asyncio
import time
import json
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta

from self_healing import (
    SelfHealingAgent, HealthStatus, FailureType, RecoveryAction,
    HealthMetrics, CircuitBreaker
)
from enhanced_agent import EnhancedLangGraphMCPAgent, AgentState
from mcp_config import get_safe_mcp_configs, get_mcp_server_configs
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage

class SelfHealingEnhancedAgent(EnhancedLangGraphMCPAgent):
    """Enhanced agent with integrated self-healing capabilities."""
    
    def __init__(self):
        super().__init__()
        self.healing_agent = SelfHealingAgent(check_interval=30.0)
        self.server_health_checks = {}
        self.tool_circuit_breakers = {}
        self.recovery_attempts = {}
        self.logger = logging.getLogger(__name__)
        
    async def initialize(self, server_configs: Dict[str, Dict[str, Any]] = None):
        """Initialize the enhanced agent with self-healing capabilities."""
        
        # Initialize the healing system first
        await self.healing_agent.initialize()
        
        # Setup model with healing protection
        await self._setup_model_with_healing()
        
        # Default server configuration if none provided
        if server_configs is None:
            server_configs = get_safe_mcp_configs()
        
        # Setup MCP client with healing
        await self._setup_mcp_client_with_healing(server_configs)
        
        # Get tools with healing protection
        await self._setup_tools_with_healing()
        
        # Build the enhanced graph
        self._build_graph()
        
        # Register all components for health monitoring
        await self._register_health_monitoring()
        
        print("✅ Self-Healing Enhanced LangGraph agent created successfully")
        
    async def _setup_model_with_healing(self):
        """Setup the language model with self-healing protection."""
        try:
            self._setup_model()
            
            # Register model health check
            async def model_health_check():
                try:
                    test_response = await asyncio.wait_for(
                        self.model.ainvoke([HumanMessage(content="test")]),
                        timeout=10.0
                    )
                    return True
                except Exception:
                    return False
                    
            self.healing_agent.register_component(
                "openrouter_model",
                health_check=model_health_check,
                recovery_strategies=[self._recover_model_connection]
            )
            
            # Setup circuit breaker for model calls
            self.model_circuit_breaker = self.healing_agent.get_circuit_breaker(
                "openrouter_model", failure_threshold=3, recovery_timeout=60.0
            )
            
        except Exception as e:
            self.logger.error(f"Failed to setup model with healing: {e}")
            raise
            
    async def _setup_mcp_client_with_healing(self, server_configs: Dict[str, Dict[str, Any]]):
        """Setup MCP client with self-healing protection."""
        from mcp_config import clean_config_for_mcp
        
        # Clean configuration for MCP client
        clean_configs = clean_config_for_mcp(server_configs)
        
        try:
            # Setup MCP client
            self._setup_mcp_client(clean_configs)
            
            # Register health checks for each MCP server
            for server_name in clean_configs.keys():
                await self._register_mcp_server_health(server_name)
                
        except Exception as e:
            self.logger.error(f"Failed to setup MCP client with healing: {e}")
            # Fallback to minimal configuration
            await self._fallback_to_minimal_config()
            
    async def _register_mcp_server_health(self, server_name: str):
        """Register health monitoring for an MCP server."""
        
        async def server_health_check():
            try:
                # Try to get tools from the server as a health check
                if self.client:
                    tools = await asyncio.wait_for(self.client.get_tools(), timeout=10.0)
                    return len(tools) > 0
                return False
            except Exception:
                return False
                
        # Register recovery strategies
        recovery_strategies = [
            lambda comp, ft: self._restart_mcp_server(server_name),
            lambda comp, ft: self._fallback_to_minimal_config()
        ]
        
        self.healing_agent.register_component(
            f"mcp_server_{server_name}",
            health_check=server_health_check,
            recovery_strategies=recovery_strategies
        )
        
    async def _setup_tools_with_healing(self):
        """Setup tools with self-healing protection."""
        try:
            self.tools = await self.client.get_tools()
            print(f"✅ Loaded {len(self.tools)} tools from MCP servers")
            
            # Register health monitoring for tool categories
            await self._register_tool_health_monitoring()
            
            # Create React agent with healing
            self._create_react_agent()
            
        except Exception as e:
            self.logger.error(f"Failed to setup tools: {e}")
            await self._fallback_to_minimal_config()
            
    async def _register_tool_health_monitoring(self):
        """Register health monitoring for different tool categories."""
        
        # Group tools by category
        tool_categories = {
            "utility_tools": [t for t in self.tools if any(name in t.name for name in ["add", "multiply", "text", "hash"])],
            "search_tools": [t for t in self.tools if any(name in t.name for name in ["search", "tavily", "duckduckgo"])],
            "blockchain_tools": [t for t in self.tools if any(name in t.name for name in ["evm_", "solana_", "moralis"])],
            "thinking_tools": [t for t in self.tools if "thinking" in t.name]
        }
        
        for category, tools in tool_categories.items():
            if tools:
                # Create health check for tool category
                async def category_health_check(cat_tools=tools):
                    try:
                        # Test a simple tool from the category
                        if cat_tools:
                            test_tool = cat_tools[0]
                            # This is a basic connectivity test
                            return True
                    except Exception:
                        return False
                        
                # Register fallback chains for tool categories
                fallbacks = []
                if category == "search_tools":
                    fallbacks = ["duckduckgo_search", "utility_tools"]
                elif category == "blockchain_tools":
                    fallbacks = ["utility_tools"]
                    
                self.healing_agent.register_component(
                    category,
                    health_check=category_health_check,
                    fallbacks=fallbacks,
                    recovery_strategies=[
                        lambda comp, ft: self._recover_tool_category(category),
                        lambda comp, ft: self._fallback_tool_execution(category)
                    ]
                )
                
    async def _register_health_monitoring(self):
        """Register comprehensive health monitoring for all components."""
        
        # Register external API health checks
        await self._register_external_api_health()
        
        # Register system resource monitoring
        await self._register_system_health()
        
        print("✅ Health monitoring registered for all components")
        
    async def _register_external_api_health(self):
        """Register health monitoring for external APIs."""
        
        # Tavily API health check
        async def tavily_health_check():
            try:
                import os
                api_key = os.getenv("TAVILY_API_KEY")
                if not api_key:
                    return False
                    
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    # Simple connectivity test
                    async with session.get("https://api.tavily.com/", timeout=5) as response:
                        return response.status < 500
            except Exception:
                return False
                
        # Moralis API health check
        async def moralis_health_check():
            try:
                import os
                api_key = os.getenv("MORALIS_API_KEY")
                if not api_key:
                    return False
                    
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    headers = {"X-API-Key": api_key}
                    async with session.get("https://deep-index.moralis.io/api/v2/dateToBlock", 
                                         headers=headers, timeout=5) as response:
                        return response.status < 500
            except Exception:
                return False
                
        self.healing_agent.register_component("tavily_api", health_check=tavily_health_check)
        self.healing_agent.register_component("moralis_api", health_check=moralis_health_check)
        
    async def _register_system_health(self):
        """Register system resource health monitoring."""
        
        async def system_health_check():
            try:
                import psutil
                memory = psutil.virtual_memory()
                cpu = psutil.cpu_percent(interval=0.1)
                
                # System is healthy if memory < 90% and CPU < 95%
                return memory.percent < 90 and cpu < 95
            except Exception:
                return False
                
        self.healing_agent.register_component(
            "system_resources",
            health_check=system_health_check,
            recovery_strategies=[self._cleanup_system_resources]
        )
        
    # Recovery Methods
    async def _recover_model_connection(self, component: str, failure_type: FailureType) -> bool:
        """Recover model connection."""
        try:
            self.logger.info("Attempting to recover model connection")
            
            # Reinitialize the model
            self._setup_model()
            
            # Test the connection
            test_response = await asyncio.wait_for(
                self.model.ainvoke([HumanMessage(content="test")]),
                timeout=10.0
            )
            
            return True
        except Exception as e:
            self.logger.error(f"Model recovery failed: {e}")
            return False
            
    async def _restart_mcp_server(self, server_name: str) -> bool:
        """Attempt to restart an MCP server."""
        try:
            self.logger.info(f"Attempting to restart MCP server: {server_name}")
            
            # Get fresh server configurations
            server_configs = get_mcp_server_configs(include_external=True)
            
            if server_name in server_configs:
                # Reinitialize the specific server
                from mcp_config import clean_config_for_mcp
                clean_configs = clean_config_for_mcp({server_name: server_configs[server_name]})
                
                # This is a simplified restart - in practice, you might need more sophisticated logic
                await asyncio.sleep(2)  # Brief pause
                
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"Server restart failed for {server_name}: {e}")
            return False
            
    async def _fallback_to_minimal_config(self) -> bool:
        """Fallback to minimal configuration."""
        try:
            self.logger.info("Falling back to minimal configuration")
            
            from mcp_config import MINIMAL_CONFIG, clean_config_for_mcp
            clean_minimal = clean_config_for_mcp(MINIMAL_CONFIG)
            
            self._setup_mcp_client(clean_minimal)
            self.tools = await self.client.get_tools()
            
            print(f"✅ Fallback successful: Loaded {len(self.tools)} tools from local server only")
            return True
            
        except Exception as e:
            self.logger.error(f"Fallback to minimal config failed: {e}")
            return False
            
    async def _recover_tool_category(self, category: str) -> bool:
        """Recover a specific tool category."""
        try:
            self.logger.info(f"Attempting to recover tool category: {category}")
            
            # Refresh tools from MCP servers
            self.tools = await self.client.get_tools()
            
            # Verify the category has tools
            if category == "utility_tools":
                return any("add" in t.name or "text" in t.name for t in self.tools)
            elif category == "search_tools":
                return any("search" in t.name for t in self.tools)
            elif category == "blockchain_tools":
                return any("evm_" in t.name or "solana_" in t.name for t in self.tools)
            elif category == "thinking_tools":
                return any("thinking" in t.name for t in self.tools)
                
            return True
            
        except Exception as e:
            self.logger.error(f"Tool category recovery failed for {category}: {e}")
            return False
            
    async def _fallback_tool_execution(self, category: str) -> bool:
        """Provide fallback tool execution for a category."""
        try:
            self.logger.info(f"Setting up fallback execution for {category}")
            
            # This would implement category-specific fallback logic
            # For now, we'll just ensure utility tools are available
            if category != "utility_tools":
                utility_tools = [t for t in self.tools if any(name in t.name for name in ["add", "multiply", "text"])]
                return len(utility_tools) > 0
                
            return True
            
        except Exception as e:
            self.logger.error(f"Fallback tool execution setup failed for {category}: {e}")
            return False
            
    async def _cleanup_system_resources(self, component: str, failure_type: FailureType) -> bool:
        """Clean up system resources."""
        try:
            self.logger.info("Attempting system resource cleanup")
            
            import gc
            gc.collect()  # Force garbage collection
            
            # Additional cleanup could be added here
            await asyncio.sleep(1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"System cleanup failed: {e}")
            return False

    # Enhanced Tool Execution with Healing
    async def _enhanced_tool_node_with_healing(self, state: AgentState) -> AgentState:
        """Enhanced tool node with self-healing protection."""
        messages = state.get("messages", [])

        try:
            # Execute tools with healing protection
            result = await self.healing_agent.execute_with_healing(
                "tool_execution",
                self._execute_tools_safely,
                messages
            )

            # Update state with results
            updated_state = {
                **state,
                "messages": result.get("messages", messages)
            }

            # Process tool outputs with healing
            final_state = await self.healing_agent.execute_with_healing(
                "tool_processing",
                self._process_tool_outputs_safely,
                updated_state
            )

            return final_state

        except Exception as e:
            self.logger.error(f"Tool execution with healing failed: {e}")

            # Fallback to basic tool execution
            return await self._fallback_tool_execution_state(state)

    async def _execute_tools_safely(self, messages: List) -> Dict[str, Any]:
        """Execute tools with safety checks."""
        from langgraph.prebuilt import ToolNode

        tool_node = ToolNode(self.tools)
        messages_state = {"messages": messages}

        # Execute with timeout
        result = await asyncio.wait_for(
            tool_node.ainvoke(messages_state),
            timeout=30.0
        )

        return result

    async def _process_tool_outputs_safely(self, state: AgentState) -> AgentState:
        """Process tool outputs with safety checks."""
        return self._process_tool_outputs(state)

    async def _fallback_tool_execution_state(self, state: AgentState) -> AgentState:
        """Fallback tool execution when healing fails."""
        self.logger.warning("Using fallback tool execution")

        # Return state with error message
        error_message = ToolMessage(
            content="Tool execution failed, but system is recovering. Please try again.",
            tool_call_id="fallback"
        )

        return {
            **state,
            "messages": state.get("messages", []) + [error_message]
        }

    # Enhanced Invoke with Healing
    async def invoke(self, message: str, config=None) -> Dict[str, Any]:
        """Invoke the enhanced agent with self-healing protection."""
        start_time = time.time()

        try:
            # Execute with healing protection
            result = await self.healing_agent.execute_with_healing(
                "agent_invoke",
                self._invoke_safely,
                message,
                config
            )

            # Add healing metadata to result
            result["healing_metadata"] = {
                "execution_time": time.time() - start_time,
                "health_status": self.get_health_status(),
                "recovery_events": self.get_recent_recovery_events()
            }

            return result

        except Exception as e:
            self.logger.error(f"Agent invocation failed: {e}")

            # Return error response with healing information
            return {
                "input": message,
                "final_response": f"I encountered an error but my self-healing systems are working to resolve it. Error: {str(e)}",
                "error": True,
                "healing_metadata": {
                    "execution_time": time.time() - start_time,
                    "health_status": self.get_health_status(),
                    "recovery_events": self.get_recent_recovery_events()
                }
            }

    async def _invoke_safely(self, message: str, config=None) -> Dict[str, Any]:
        """Safely invoke the parent agent."""
        return await super().invoke(message, config)

    # Health Status and Reporting
    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status of all components."""
        return self.healing_agent.get_health_report()

    def get_recent_recovery_events(self) -> List[Dict[str, Any]]:
        """Get recent recovery events."""
        recent_events = []

        # Get events from the last hour
        cutoff_time = datetime.now() - timedelta(hours=1)

        for event in self.healing_agent.recovery_manager.recovery_history:
            if event["timestamp"] > cutoff_time:
                recent_events.append({
                    "timestamp": event["timestamp"].isoformat(),
                    "component": event["component"],
                    "failure_type": event["failure_type"],
                    "recovery_time": event["recovery_time"],
                    "success": event["success"]
                })

        return recent_events

    def get_circuit_breaker_status(self) -> Dict[str, str]:
        """Get status of all circuit breakers."""
        return {
            name: breaker.state
            for name, breaker in self.healing_agent.health_monitor.circuit_breakers.items()
        }

    async def force_health_check(self) -> Dict[str, Any]:
        """Force a health check of all components."""
        await self.healing_agent.health_monitor._check_all_components()
        return self.get_health_status()

    async def trigger_recovery(self, component: str) -> bool:
        """Manually trigger recovery for a specific component."""
        try:
            return await self.healing_agent.recovery_manager.attempt_recovery(
                component, FailureType.CONNECTION_FAILURE
            )
        except Exception as e:
            self.logger.error(f"Manual recovery trigger failed for {component}: {e}")
            return False

    # Override the graph building to use healing-enabled nodes
    def _build_graph(self):
        """Build the enhanced graph with self-healing nodes."""
        from langgraph.graph import StateGraph, END

        # Create the state graph
        workflow = StateGraph(AgentState)

        # Add nodes (using healing-enabled tool node)
        workflow.add_node("router", self._query_router_node)
        workflow.add_node("planner", self._task_planning_node)
        workflow.add_node("tools", self._enhanced_tool_node_with_healing)  # Healing-enabled
        workflow.add_node("direct_executor", self._direct_execution_node)
        workflow.add_node("synthesizer", self._synthesis_node)

        # Define the flow (same as parent)
        workflow.set_entry_point("router")

        workflow.add_conditional_edges(
            "router",
            self._route_from_router,
            {
                "direct": "direct_executor",
                "multi_step": "planner"
            }
        )

        workflow.add_edge("direct_executor", END)

        workflow.add_conditional_edges(
            "planner",
            self._route_from_planner,
            {
                "tools": "tools",
                "synthesizer": "synthesizer"
            }
        )

        workflow.add_edge("tools", "planner")
        workflow.add_edge("synthesizer", END)

        # Compile the graph
        self.graph = workflow.compile()

        print("✅ Self-healing enhanced reasoning graph compiled successfully")

    async def cleanup(self):
        """Cleanup resources including healing system."""
        await self.healing_agent.shutdown()
        await super().cleanup()

# Utility function for easy self-healing agent creation
async def create_self_healing_agent(server_configs: Dict[str, Dict[str, Any]] = None) -> SelfHealingEnhancedAgent:
    """Create and initialize a Self-Healing Enhanced LangGraph MCP agent."""
    agent = SelfHealingEnhancedAgent()
    await agent.initialize(server_configs)
    return agent

    # Enhanced Tool Execution with Healing
    async def _enhanced_tool_node_with_healing(self, state: AgentState) -> AgentState:
        """Enhanced tool node with self-healing protection."""
        messages = state.get("messages", [])

        try:
            # Execute tools with healing protection
            result = await self.healing_agent.execute_with_healing(
                "tool_execution",
                self._execute_tools_safely,
                messages
            )

            # Update state with results
            updated_state = {
                **state,
                "messages": result.get("messages", messages)
            }

            # Process tool outputs with healing
            final_state = await self.healing_agent.execute_with_healing(
                "tool_processing",
                self._process_tool_outputs_safely,
                updated_state
            )

            return final_state

        except Exception as e:
            self.logger.error(f"Tool execution with healing failed: {e}")

            # Fallback to basic tool execution
            return await self._fallback_tool_execution_state(state)

    async def _execute_tools_safely(self, messages: List) -> Dict[str, Any]:
        """Execute tools with safety checks."""
        from langgraph.prebuilt import ToolNode

        tool_node = ToolNode(self.tools)
        messages_state = {"messages": messages}

        # Execute with timeout
        result = await asyncio.wait_for(
            tool_node.ainvoke(messages_state),
            timeout=30.0
        )

        return result

    async def _process_tool_outputs_safely(self, state: AgentState) -> AgentState:
        """Process tool outputs with safety checks."""
        return self._process_tool_outputs(state)

    async def _fallback_tool_execution_state(self, state: AgentState) -> AgentState:
        """Fallback tool execution when healing fails."""
        self.logger.warning("Using fallback tool execution")

        # Return state with error message
        error_message = ToolMessage(
            content="Tool execution failed, but system is recovering. Please try again.",
            tool_call_id="fallback"
        )

        return {
            **state,
            "messages": state.get("messages", []) + [error_message]
        }

    # Enhanced Invoke with Healing
    async def invoke(self, message: str, config=None) -> Dict[str, Any]:
        """Invoke the enhanced agent with self-healing protection."""
        start_time = time.time()

        try:
            # Execute with healing protection
            result = await self.healing_agent.execute_with_healing(
                "agent_invoke",
                self._invoke_safely,
                message,
                config
            )

            # Add healing metadata to result
            result["healing_metadata"] = {
                "execution_time": time.time() - start_time,
                "health_status": self.get_health_status(),
                "recovery_events": self.get_recent_recovery_events()
            }

            return result

        except Exception as e:
            self.logger.error(f"Agent invocation failed: {e}")

            # Return error response with healing information
            return {
                "input": message,
                "final_response": f"I encountered an error but my self-healing systems are working to resolve it. Error: {str(e)}",
                "error": True,
                "healing_metadata": {
                    "execution_time": time.time() - start_time,
                    "health_status": self.get_health_status(),
                    "recovery_events": self.get_recent_recovery_events()
                }
            }

    async def _invoke_safely(self, message: str, config=None) -> Dict[str, Any]:
        """Safely invoke the parent agent."""
        return await super().invoke(message, config)

    # Health Status and Reporting
    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status of all components."""
        return self.healing_agent.get_health_report()

    def get_recent_recovery_events(self) -> List[Dict[str, Any]]:
        """Get recent recovery events."""
        recent_events = []

        # Get events from the last hour
        cutoff_time = datetime.now() - timedelta(hours=1)

        for event in self.healing_agent.recovery_manager.recovery_history:
            if event["timestamp"] > cutoff_time:
                recent_events.append({
                    "timestamp": event["timestamp"].isoformat(),
                    "component": event["component"],
                    "failure_type": event["failure_type"],
                    "recovery_time": event["recovery_time"],
                    "success": event["success"]
                })

        return recent_events

    def get_circuit_breaker_status(self) -> Dict[str, str]:
        """Get status of all circuit breakers."""
        return {
            name: breaker.state
            for name, breaker in self.healing_agent.health_monitor.circuit_breakers.items()
        }

    async def force_health_check(self) -> Dict[str, Any]:
        """Force a health check of all components."""
        await self.healing_agent.health_monitor._check_all_components()
        return self.get_health_status()

    async def trigger_recovery(self, component: str) -> bool:
        """Manually trigger recovery for a specific component."""
        try:
            return await self.healing_agent.recovery_manager.attempt_recovery(
                component, FailureType.CONNECTION_FAILURE
            )
        except Exception as e:
            self.logger.error(f"Manual recovery trigger failed for {component}: {e}")
            return False

    # Override the graph building to use healing-enabled nodes
    def _build_graph(self):
        """Build the enhanced graph with self-healing nodes."""
        from langgraph.graph import StateGraph, END

        # Create the state graph
        workflow = StateGraph(AgentState)

        # Add nodes (using healing-enabled tool node)
        workflow.add_node("router", self._query_router_node)
        workflow.add_node("planner", self._task_planning_node)
        workflow.add_node("tools", self._enhanced_tool_node_with_healing)  # Healing-enabled
        workflow.add_node("direct_executor", self._direct_execution_node)
        workflow.add_node("synthesizer", self._synthesis_node)

        # Define the flow (same as parent)
        workflow.set_entry_point("router")

        workflow.add_conditional_edges(
            "router",
            self._route_from_router,
            {
                "direct": "direct_executor",
                "multi_step": "planner"
            }
        )

        workflow.add_edge("direct_executor", END)

        workflow.add_conditional_edges(
            "planner",
            self._route_from_planner,
            {
                "tools": "tools",
                "synthesizer": "synthesizer"
            }
        )

        workflow.add_edge("tools", "planner")
        workflow.add_edge("synthesizer", END)

        # Compile the graph
        self.graph = workflow.compile()

        print("✅ Self-healing enhanced reasoning graph compiled successfully")

    async def cleanup(self):
        """Cleanup resources including healing system."""
        await self.healing_agent.shutdown()
        await super().cleanup()

# Utility function for easy self-healing agent creation
async def create_self_healing_agent(server_configs: Dict[str, Dict[str, Any]] = None) -> SelfHealingEnhancedAgent:
    """Create and initialize a Self-Healing Enhanced LangGraph MCP agent."""
    agent = SelfHealingEnhancedAgent()
    await agent.initialize(server_configs)
    return agent
