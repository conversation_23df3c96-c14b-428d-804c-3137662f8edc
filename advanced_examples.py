#!/usr/bin/env python3
"""
Advanced Examples for LangGraph MCP Agent
Demonstrates the full capabilities with Sequential Thinking and Moralis integration.
"""

import asyncio
import os
from agent import create_agent

async def sequential_thinking_example():
    """Example using Sequential Thinking for complex analysis."""
    print("🧠 Sequential Thinking Example")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_agent()
        
        query = """Use sequential thinking to analyze: 
        What are the key factors to consider when evaluating an NFT collection for investment? 
        Think through this step by step."""
        
        print(f"Query: {query}")
        print("\nAgent Response:")
        
        response = await agent.invoke(query)
        print(response['messages'][-1].content)
        
    finally:
        if agent:
            await agent.cleanup()

async def blockchain_analysis_example():
    """Example using Moralis for blockchain data analysis."""
    print("\n🌐 Blockchain Analysis Example")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_agent()
        
        # Example wallet address (<PERSON><PERSON>'s wallet)
        wallet = "******************************************"
        
        query = f"""Analyze this Ethereum wallet: {wallet}
        Please get:
        1. The wallet's net worth
        2. Token balances with prices
        3. Any NFTs owned
        
        Provide a comprehensive summary."""
        
        print(f"Query: {query}")
        print("\nAgent Response:")
        
        response = await agent.invoke(query)
        print(response['messages'][-1].content)
        
    finally:
        if agent:
            await agent.cleanup()

async def multi_tool_example():
    """Example combining multiple tool types."""
    print("\n🔧 Multi-Tool Integration Example")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_agent()
        
        query = """I have a crypto address: ******************************************
        
        Please:
        1. First analyze what type of address this is
        2. Get the NFTs for this wallet
        3. Calculate the total value if possible
        4. Use sequential thinking to recommend next steps for portfolio analysis
        
        Be thorough and explain each step."""
        
        print(f"Query: {query}")
        print("\nAgent Response:")
        
        response = await agent.invoke(query)
        print(response['messages'][-1].content)
        
    finally:
        if agent:
            await agent.cleanup()

async def solana_example():
    """Example using Solana-specific tools."""
    print("\n☀️ Solana Analysis Example")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_agent()
        
        # Example Solana wallet
        solana_wallet = "DJvE3EuZHwEzp2FwjKjLVkCXUj5srjqiDM"
        
        query = f"""Analyze this Solana wallet: {solana_wallet}
        
        Please get:
        1. The portfolio overview
        2. SPL token balances
        3. Any Solana NFTs
        
        Compare the capabilities between Ethereum and Solana analysis."""
        
        print(f"Query: {query}")
        print("\nAgent Response:")
        
        response = await agent.invoke(query)
        print(response['messages'][-1].content)
        
    finally:
        if agent:
            await agent.cleanup()

async def defi_analysis_example():
    """Example using DeFi analysis tools."""
    print("\n💰 DeFi Analysis Example")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_agent()
        
        # Example DeFi wallet
        defi_wallet = "******************************************"
        
        query = f"""Analyze the DeFi positions for wallet: {defi_wallet}
        
        Please:
        1. Get a DeFi summary
        2. Check for any active positions
        3. Use sequential thinking to evaluate the DeFi strategy
        
        Provide insights on the portfolio composition."""
        
        print(f"Query: {query}")
        print("\nAgent Response:")
        
        response = await agent.invoke(query)
        print(response['messages'][-1].content)
        
    finally:
        if agent:
            await agent.cleanup()

async def tavily_web_search_example():
    """Example using Tavily for web search and research."""
    print("\n🔍 Tavily Web Search Example")
    print("=" * 50)

    agent = None
    try:
        agent = await create_agent()

        query = """Search for the latest news about Ethereum 2.0 staking rewards and APY rates.
        Then use sequential thinking to analyze whether staking is a good investment strategy."""

        print(f"Query: {query}")
        print("\nAgent Response:")

        response = await agent.invoke(query)
        print(response['messages'][-1].content)

    finally:
        if agent:
            await agent.cleanup()

async def web_extraction_example():
    """Example using Tavily for content extraction."""
    print("\n📄 Web Content Extraction Example")
    print("=" * 50)

    agent = None
    try:
        agent = await create_agent()

        query = """Extract key information from CoinGecko's Bitcoin page and summarize:
        1. Current price trends
        2. Market cap information
        3. Recent news or updates

        Then compare this with blockchain data from Moralis if possible."""

        print(f"Query: {query}")
        print("\nAgent Response:")

        response = await agent.invoke(query)
        print(response['messages'][-1].content)

    finally:
        if agent:
            await agent.cleanup()

async def tool_discovery_example():
    """Example showing tool discovery and capabilities."""
    print("\n🔍 Tool Discovery Example")
    print("=" * 50)
    
    agent = None
    try:
        agent = await create_agent()
        
        query = """Please list all available tools and categorize them by functionality. 
        Then use sequential thinking to explain how these tools can work together 
        for comprehensive crypto portfolio analysis."""
        
        print(f"Query: {query}")
        print("\nAgent Response:")
        
        response = await agent.invoke(query)
        print(response['messages'][-1].content)
        
    finally:
        if agent:
            await agent.cleanup()

async def main():
    """Run all advanced examples."""
    print("🚀 Advanced LangGraph MCP Agent Examples")
    print("=" * 60)
    
    # Check environment
    if not os.getenv("OPENROUTER_API_KEY"):
        print("❌ Error: OPENROUTER_API_KEY not found")
        return
    
    if not os.getenv("MORALIS_API_KEY"):
        print("⚠️  Warning: MORALIS_API_KEY not found - Moralis examples may not work")

    if not os.getenv("TAVILY_API_KEY"):
        print("⚠️  Warning: TAVILY_API_KEY not found - Tavily examples may not work")
    
    try:
        # Run examples
        await sequential_thinking_example()
        await tavily_web_search_example()
        await web_extraction_example()
        await blockchain_analysis_example()
        await multi_tool_example()
        await solana_example()
        await defi_analysis_example()
        await tool_discovery_example()
        
        print("\n✅ All examples completed!")
        print("\nTo run these interactively, use: python cli.py")
        
    except KeyboardInterrupt:
        print("\n👋 Examples interrupted by user")
    except Exception as e:
        print(f"❌ Error running examples: {e}")

if __name__ == "__main__":
    asyncio.run(main())
