#!/usr/bin/env python3
"""
MCP Server Configuration
Centralized configuration for all MCP servers used by the agent.
"""

import os
from typing import Dict, Any, Optional

def get_mcp_server_configs(include_external: bool = True) -> Dict[str, Dict[str, Any]]:
    """
    Get MCP server configurations.
    
    Args:
        include_external: Whether to include external MCP servers (Sequential Thinking, Moralis)
    
    Returns:
        Dictionary of server configurations
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Base configuration with local utility server
    configs = {
        "utility_server": {
            "command": "python",
            "args": [os.path.join(current_dir, "simple_mcp_server.py")],
            "transport": "stdio"
        }
    }

    # Store descriptions separately for display purposes
    descriptions = {
        "utility_server": "Local utility server with math, text, and crypto tools"
    }
    
    if not include_external:
        return configs
    
    # Add Sequential Thinking server
    configs["sequential_thinking"] = {
        "command": "npx",
        "args": [
            "-y",
            "@modelcontextprotocol/server-sequential-thinking"
        ],
        "transport": "stdio"
    }
    descriptions["sequential_thinking"] = "Advanced reasoning and problem-solving tools"

    # Add Tavily server using remote MCP endpoint (preferred method)
    tavily_api_key = os.getenv("TAVILY_API_KEY")
    if tavily_api_key:
        configs["tavily"] = {
            "command": "npx",
            "args": ["-y", "mcp-remote", f"https://mcp.tavily.com/mcp/?tavilyApiKey={tavily_api_key}"],
            "transport": "stdio"
        }
        descriptions["tavily"] = "Web search and data extraction via Tavily API (remote)"

    # Add DuckDuckGo search as backup (no API key required)
    configs["duckduckgo"] = {
        "command": "uvx",
        "args": ["duckduckgo-mcp-server"],
        "transport": "stdio"
    }
    descriptions["duckduckgo"] = "Web search via DuckDuckGo (backup search engine)"

    # Add Moralis server if API key is available
    moralis_api_key = os.getenv("MORALIS_API_KEY")
    if moralis_api_key:
        configs["moralis"] = {
            "command": "npx",
            "args": ["@moralisweb3/api-mcp-server"],
            "transport": "stdio",
            "env": {
                "MORALIS_API_KEY": moralis_api_key
            }
        }
        descriptions["moralis"] = "Blockchain data and analytics via Moralis API"

    # Add descriptions to configs for display (but remove before passing to MCP client)
    for name in configs:
        configs[name]["_description"] = descriptions.get(name, "No description")

    return configs

def get_safe_mcp_configs() -> Dict[str, Dict[str, Any]]:
    """
    Get MCP configurations with fallback to local-only if external servers fail.
    """
    try:
        # Try to get full configuration
        return get_mcp_server_configs(include_external=True)
    except Exception as e:
        print(f"⚠️  Warning: Could not configure external MCP servers: {e}")
        print("📦 Falling back to local utility server only")
        return get_mcp_server_configs(include_external=False)

def print_server_info(configs: Dict[str, Dict[str, Any]]):
    """Print information about configured MCP servers."""
    print(f"\n🔧 Configured MCP Servers ({len(configs)}):")

    for name, config in configs.items():
        description = config.get("_description", "No description")
        command = config.get("command", "Unknown")

        if "env" in config:
            env_vars = list(config["env"].keys())
            print(f"  📡 {name}: {description} (command: {command}, env: {env_vars})")
        else:
            print(f"  🔧 {name}: {description} (command: {command})")

def clean_config_for_mcp(configs: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    """Remove display-only fields from config before passing to MCP client."""
    cleaned = {}
    for name, config in configs.items():
        cleaned[name] = {k: v for k, v in config.items() if not k.startswith("_")}
    return cleaned

# Predefined configurations for different use cases
MINIMAL_CONFIG = {
    "utility_server": {
        "command": "python",
        "args": [os.path.join(os.path.dirname(os.path.abspath(__file__)), "simple_mcp_server.py")],
        "transport": "stdio",
        "_description": "Local utility server only"
    }
}

FULL_CONFIG_TEMPLATE = {
    "utility_server": {
        "command": "python", 
        "args": ["./simple_mcp_server.py"],
        "transport": "stdio"
    },
    "sequential_thinking": {
        "command": "npx",
        "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
        "transport": "stdio"
    },
    "moralis": {
        "command": "npx",
        "args": ["@moralisweb3/api-mcp-server"],
        "transport": "stdio",
        "env": {
            "MORALIS_API_KEY": "your_moralis_api_key_here"
        }
    }
}
