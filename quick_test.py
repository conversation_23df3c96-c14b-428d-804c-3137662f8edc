#!/usr/bin/env python3
"""
Quick test of the enhanced agent with different execution profiles.
"""

import asyncio
import sys
from enhanced_agent import create_enhanced_agent
from langchain_core.runnables import RunnableConfig

async def test_profiles():
    """Test different execution profiles."""
    print("🧪 Testing Enhanced Agent with Dynamic Reasoning")
    print("=" * 50)
    
    try:
        # Initialize agent
        print("Initializing enhanced agent...")
        agent = await create_enhanced_agent()
        print("✅ Agent initialized successfully\n")
        
        # Test queries with different profiles
        test_cases = [
            ("fast", "What's 2 + 2?"),
            ("balanced", "EUxS8Kqvbe8zDr8bG1RYtgjVNzEhMTG6B6H3WwrCHfwA"),
            ("thorough", "Search for Bitcoin news")
        ]
        
        for profile, query in test_cases:
            print(f"🎯 Testing {profile.upper()} profile:")
            print(f"Query: {query}")
            print("-" * 30)
            
            config = RunnableConfig(recursion_limit=50, thread_id=f"test_{profile}")
            
            try:
                result = await agent.invoke(query, execution_profile=profile, config=config)
                
                print(f"✅ Success!")
                print(f"  Intent: {result.get('intent', 'unknown')}")
                print(f"  Mode: {result.get('execution_mode', 'unknown')}")
                print(f"  Next Action: {result.get('next_action', 'unknown')}")
                print(f"  Reflection Depth: {result.get('reflection_depth', 0)}")
                print(f"  Response: {result.get('final_response', 'No response')[:100]}...")
                
                reasoning = result.get('reasoning_history', [])
                if reasoning:
                    print(f"  Reasoning Steps: {len(reasoning)}")
                    print(f"  First Step: {reasoning[0] if reasoning else 'None'}")
                
            except Exception as e:
                print(f"❌ Error with {profile} profile: {e}")
            
            print()
        
        await agent.cleanup()
        print("✅ All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the test."""
    success = await test_profiles()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
