# LangGraph Agent with MCP Integration

This project demonstrates how to create a LangGraph agent with Model Context Protocol (MCP) capabilities. The agent can use various tools through MCP servers to perform calculations, text processing, JSON operations, and crypto-related tasks.

## Features

- **LangGraph Agent**: ReAct-style agent with tool calling capabilities
- **Multi-Server MCP Integration**: Connect to multiple MCP servers simultaneously
- **OpenRouter Support**: Uses your existing OpenRouter configuration
- **114 Built-in Tools** across 5 specialized servers:
  - **Utility Tools**: Math, text processing, JSON handling, crypto address analysis
  - **Sequential Thinking**: Advanced reasoning and problem-solving capabilities
  - **Dual Web Search**: Tavily (primary) + DuckDuckGo (backup) for comprehensive web research
  - **Moralis Integration**: Comprehensive blockchain data, NFTs, tokens, and analytics
- **Streaming Support**: Real-time response streaming
- **Memory**: Conversation history with MemorySaver
- **Robust Error Handling**: Graceful fallback if external servers are unavailable

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Environment Configuration

Your `.env` file should already contain the OpenRouter configuration:

```env
OPENROUTER_API_KEY=your_api_key_here
OPENROUTER_MODEL=moonshotai/kimi-k2
```

### 3. Start Chatting with Your Agent

**Option 1: Simple Command (Recommended)**
```bash
python cli.py
```

**Option 2: Even Simpler (Windows)**
```bash
run_agent.bat
```

**Option 3: Alternative Launcher**
```bash
python chat.py
```

## Project Structure

```
├── .env                    # Environment variables (already configured)
├── requirements.txt        # Python dependencies
├── simple_mcp_server.py   # Basic MCP server with utility tools
├── agent.py               # Main LangGraph agent with MCP integration
├── cli.py                 # Interactive CLI interface (main entry point)
├── chat.py                # Simple launcher script
├── run_agent.bat          # Windows batch file launcher
├── run_agent.sh           # Unix/Linux shell script launcher
├── example.py             # Example usage and demonstrations
└── README.md              # This file
```

## Available Tools (114 Total)

The agent has access to tools from five specialized MCP servers:

### 🔧 Utility Server (14 tools)
**Math Operations:**
- `add(a, b)` - Add two numbers
- `multiply(a, b)` - Multiply two numbers
- `divide(a, b)` - Divide two numbers
- `calculate_percentage(value, total)` - Calculate percentage

**Text Processing:**
- `text_length(text)` - Get text length
- `text_uppercase(text)` - Convert to uppercase
- `text_lowercase(text)` - Convert to lowercase
- `hash_text(text, algorithm)` - Hash text (SHA256, MD5, SHA1)
- `encode_base64(text)` - Encode to base64
- `decode_base64(encoded_text)` - Decode from base64

**JSON & Crypto:**
- `format_json(json_string)` - Format and prettify JSON
- `validate_json(json_string)` - Validate JSON structure
- `crypto_address_info(address)` - Analyze crypto addresses
- `list_available_tools()` - List all available tools

### 🧠 Sequential Thinking (1 tool)
- `sequentialthinking()` - Advanced reasoning and problem-solving for complex queries

### 🔍 Web Search Engines (6 tools)
**Tavily (Primary - 4 tools):**
- `tavily_search()` - Real-time web search with intelligent results
- `tavily_extract()` - Extract content from specific web pages
- `tavily_crawl()` - Web crawling and data collection
- `tavily_map()` - Website mapping and structure analysis

**DuckDuckGo (Backup - 2 tools):**
- `search()` - Privacy-focused web search with rate limiting
- `fetch_content()` - Fetch and parse webpage content

### 🌐 Moralis Blockchain API (93 tools)
**NFT Operations:**
- `evm_getwalletnfts()` - Get NFTs owned by a wallet
- `evm_getnftmetadata()` - Get NFT metadata
- `evm_getnfttrades()` - Get NFT trading history
- `evm_getnftcontractsaleprices()` - Get NFT floor prices
- And 20+ more NFT-related tools

**Token & DeFi:**
- `evm_getwallettokenbalancesprice()` - Get token balances with USD prices
- `evm_getwalletnetworth()` - Calculate total wallet value
- `evm_gettokenmetadata()` - Get token information
- `evm_getdefisummary()` - Get DeFi positions summary
- And 15+ more token/DeFi tools

**Solana Support:**
- `solana_getportfolio()` - Get Solana wallet portfolio
- `solana_getnfts()` - Get Solana NFTs
- `solana_gettokenprice()` - Get Solana token prices
- And 15+ more Solana tools

**Blockchain Analytics:**
- `evm_getwallethistory()` - Get complete transaction history
- `evm_getwalletstats()` - Get wallet statistics
- `evm_getblock()` - Get block information
- And many more analytical tools

## Usage Examples

### Interactive Chat (Default Mode)

Simply run the CLI and start chatting:

```bash
python cli.py
```

**Example Chat Session:**
```
You: What's 25 * 4 + 100?
Agent: 25 * 4 + 100 = 200

You: Convert "Hello World" to uppercase and hash it
Agent: Results:
- Uppercase: HELLO WORLD
- SHA256 Hash: 787ec76dcafd20c1908eb0936a12f91edd105ab5cd7ecc2b1ae2032648345dff

You: tools
📋 Available Tools (14):
   1. add                    8. hash_text
   2. multiply               9. encode_base64
   3. divide                10. decode_base64
   4. calculate_percentage  11. format_json
   5. text_length           12. validate_json
   6. text_uppercase        13. crypto_address_info
   7. text_lowercase        14. list_available_tools

You: quit
👋 Goodbye!
```

### Chat Commands

- `help` - Show help information
- `tools` - List all available tools
- `clear` - Clear the screen
- `quit` or `q` - Exit the chat

### Programmatic Usage

```python
import asyncio
from agent import create_agent

async def main():
    agent = await create_agent()

    # Simple calculation
    response = await agent.invoke("What's 25 * 4 + 100?")
    print(response['messages'][-1].content)

    # Text processing
    response = await agent.invoke("Convert 'hello world' to uppercase and hash it")
    print(response['messages'][-1].content)

    await agent.cleanup()

asyncio.run(main())
```

### Streaming Responses

```python
async def stream_example():
    agent = await create_agent()
    
    async for chunk in agent.stream("Calculate 123 * 456 and encode in base64"):
        if "messages" in chunk and chunk["messages"]:
            print(chunk["messages"][-1].content, end="")
    
    await agent.cleanup()
```

### Custom MCP Server Configuration

```python
custom_config = {
    "utility_server": {
        "command": "python",
        "args": ["./simple_mcp_server.py"],
        "transport": "stdio"
    },
    "weather_server": {
        "url": "http://localhost:8000/mcp",
        "transport": "streamable_http"
    }
}

agent = await create_agent(custom_config)
```

## CLI Options

For advanced usage, the CLI supports several options:

```bash
# Interactive chat (default)
python cli.py

# Single query and exit
python cli.py --query "What's 5 * 10?"

# List tools and exit
python cli.py --tools

# Hide banner
python cli.py --no-banner
```

## Running Examples

The `example.py` file contains several demonstrations:

1. **Basic Example**: Simple math and text operations
2. **Crypto Example**: Cryptocurrency address analysis
3. **JSON Example**: JSON validation and formatting
4. **Streaming Example**: Real-time response streaming
5. **Custom Server Example**: Multiple MCP server configuration

Run all examples:
```bash
python example.py
```

## Extending the System

### Adding New Tools to MCP Server

Edit `simple_mcp_server.py` and add new functions with the `@mcp.tool()` decorator:

```python
@mcp.tool()
def my_new_tool(param: str) -> str:
    """Description of what this tool does."""
    return f"Processed: {param}"
```

### Adding External MCP Servers

You can connect to external MCP servers by modifying the server configuration:

```python
server_configs = {
    "local_server": {
        "command": "python",
        "args": ["./simple_mcp_server.py"],
        "transport": "stdio"
    },
    "remote_server": {
        "url": "http://localhost:8000/mcp",
        "transport": "streamable_http"
    }
}
```

### Using Different Models

Modify the model configuration in `agent.py`:

```python
self.model = ChatOpenAI(
    model="different/model",
    openai_api_key=api_key,
    openai_api_base="https://openrouter.ai/api/v1",
    temperature=0.1,
    max_tokens=4000
)
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **API Key Issues**: Verify your `.env` file contains the correct OpenRouter API key

3. **MCP Server Not Starting**: Check that Python can execute the MCP server script
   ```bash
   python simple_mcp_server.py
   ```

4. **Tool Not Found**: Ensure the MCP server is running and tools are properly registered

### Debug Mode

Add debug logging to see what's happening:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Next Steps

- Add more sophisticated tools to the MCP server
- Integrate with external APIs (weather, news, etc.)
- Create specialized MCP servers for different domains
- Add persistent memory with Redis or other storage
- Deploy as a web service with FastAPI

## Resources

- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
- [MCP Documentation](https://modelcontextprotocol.io/)
- [OpenRouter API](https://openrouter.ai/docs)
- [LangChain MCP Adapters](https://github.com/langchain-ai/langchain-mcp-adapters)
