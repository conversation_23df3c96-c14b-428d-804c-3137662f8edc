#!/usr/bin/env python3
"""
Enhanced CLI interface for the sophisticated LangGraph MCP Agent
Provides detailed reasoning process visibility and multi-step query handling.
"""

import asyncio
import argparse
import sys
import os
from typing import Optional
from dotenv import load_dotenv

from enhanced_agent import create_enhanced_agent, EnhancedLangGraphMCPAgent
from langchain_core.runnables import RunnableConfig

# Load environment variables
load_dotenv()

class Colors:
    """ANSI color codes for terminal output."""
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_banner():
    """Print the enhanced CLI banner."""
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║              Enhanced LangGraph MCP Agent                   ║
║           Multi-Step Reasoning Architecture                 ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
"""
    print(banner)

def print_reasoning_process(reasoning_history):
    """Print the agent's reasoning process."""
    if reasoning_history:
        print(f"\n{Colors.PURPLE}🧠 Reasoning Process:{Colors.END}")
        for i, step in enumerate(reasoning_history, 1):
            print(f"  {Colors.YELLOW}{i}.{Colors.END} {step}")

def print_information_gathered(information_gathered):
    """Print the information gathered during execution."""
    if information_gathered:
        print(f"\n{Colors.BLUE}📊 Information Gathered:{Colors.END}")
        
        if information_gathered.get("web_data"):
            print(f"  {Colors.GREEN}🌐 Web Research:{Colors.END} {information_gathered['web_data'][:200]}...")
        
        if information_gathered.get("blockchain_data"):
            print(f"  {Colors.GREEN}⛓️  Blockchain Data:{Colors.END} {information_gathered['blockchain_data'][:200]}...")
        
        if information_gathered.get("strategic_analysis"):
            print(f"  {Colors.GREEN}🎯 Strategic Analysis:{Colors.END} {information_gathered['strategic_analysis'][:200]}...")

async def interactive_mode(agent: EnhancedLangGraphMCPAgent):
    """Run the enhanced agent in interactive chat mode."""
    print(f"\n{Colors.GREEN}💬 Enhanced Interactive Mode{Colors.END}")
    print(f"{Colors.CYAN}Ask complex multi-step questions! The agent will break them down and reason through each step.{Colors.END}")
    print(f"{Colors.YELLOW}Commands: 'help' for assistance, 'tools' to list tools, 'quit' to exit{Colors.END}")
    
    config = RunnableConfig(recursion_limit=50, thread_id="enhanced_cli_session")
    
    while True:
        try:
            # Get user input
            user_input = input(f"\n{Colors.BLUE}You:{Colors.END} ").strip()
            
            if not user_input:
                continue
                
            # Handle special commands
            if user_input.lower() in ['quit', 'exit', 'q']:
                print(f"{Colors.YELLOW}👋 Goodbye!{Colors.END}")
                break
            elif user_input.lower() in ['help', 'h']:
                print_help()
                continue
            elif user_input.lower() in ['tools', 'list']:
                print_tools(agent)
                continue
            elif user_input.lower() in ['clear', 'cls']:
                os.system('cls' if os.name == 'nt' else 'clear')
                print_banner()
                continue
            
            # Process the query with enhanced reasoning
            print(f"{Colors.PURPLE}Agent:{Colors.END} Processing your request...")
            
            try:
                result = await agent.invoke(user_input, config)
                
                # Display the reasoning process
                print_reasoning_process(result.get("reasoning_history", []))
                
                # Display gathered information
                print_information_gathered(result.get("information_gathered", {}))
                
                # Display final response
                final_response = result.get("final_response", "No response generated")
                print(f"\n{Colors.GREEN}📋 Final Response:{Colors.END}")
                print(f"{final_response}")
                
                # Display metadata
                intent = result.get("intent", "unknown")
                confidence = result.get("confidence_level", 0.0)
                cycles = result.get("planning_cycles", 0)
                print(f"\n{Colors.CYAN}📈 Metadata:{Colors.END} Intent: {intent}, Confidence: {confidence:.2f}, Planning Cycles: {cycles}")
                
            except Exception as e:
                print(f"{Colors.RED}Error: {e}{Colors.END}")
                
        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}👋 Goodbye!{Colors.END}")
            break
        except EOFError:
            print(f"\n{Colors.YELLOW}👋 Goodbye!{Colors.END}")
            break

async def single_query_mode(agent: EnhancedLangGraphMCPAgent, query: str):
    """Run a single query and display detailed results."""
    print(f"\n{Colors.BLUE}Query:{Colors.END} {query}")
    print(f"{Colors.PURPLE}Processing with enhanced reasoning...{Colors.END}")
    
    try:
        result = await agent.invoke(query)
        
        # Display results with full detail
        print_reasoning_process(result.get("reasoning_history", []))
        print_information_gathered(result.get("information_gathered", {}))
        
        final_response = result.get("final_response", "No response generated")
        print(f"\n{Colors.GREEN}📋 Final Response:{Colors.END}")
        print(f"{final_response}")
        
        # Display metadata
        intent = result.get("intent", "unknown")
        confidence = result.get("confidence_level", 0.0)
        cycles = result.get("planning_cycles", 0)
        plan = result.get("plan", [])
        
        print(f"\n{Colors.CYAN}📈 Execution Metadata:{Colors.END}")
        print(f"  Intent: {intent}")
        print(f"  Confidence: {confidence:.2f}")
        print(f"  Planning Cycles: {cycles}")
        print(f"  Initial Plan: {plan}")
        
        return True
                
    except Exception as e:
        print(f"{Colors.RED}Error: {e}{Colors.END}")
        return False

def print_tools(agent: EnhancedLangGraphMCPAgent):
    """Print available tools."""
    tools = agent.list_tools()
    print(f"\n{Colors.GREEN}📋 Available Tools ({len(tools)}):{Colors.END}")
    
    # Group tools by category
    categories = {
        "Search": [t for t in tools if any(x in t.lower() for x in ['search', 'tavily'])],
        "Blockchain": [t for t in tools if any(x in t.lower() for x in ['evm_', 'solana_'])],
        "Utility": [t for t in tools if any(x in t.lower() for x in ['add', 'multiply', 'text_', 'hash_'])],
        "Reasoning": [t for t in tools if 'thinking' in t.lower()],
        "Other": []
    }
    
    # Add uncategorized tools
    categorized = set()
    for cat_tools in categories.values():
        categorized.update(cat_tools)
    categories["Other"] = [t for t in tools if t not in categorized]
    
    for category, cat_tools in categories.items():
        if cat_tools:
            print(f"\n  {Colors.YELLOW}{category}:{Colors.END}")
            for tool in cat_tools[:5]:  # Show first 5 in each category
                print(f"    - {tool}")
            if len(cat_tools) > 5:
                print(f"    ... and {len(cat_tools) - 5} more")

def print_help():
    """Print help information."""
    help_text = f"""
{Colors.GREEN}🔧 Enhanced Agent Commands:{Colors.END}

{Colors.YELLOW}Interactive Mode Commands:{Colors.END}
  help, h     - Show this help message
  tools, list - List available tools by category
  clear, cls  - Clear the screen
  quit, q     - Exit the program

{Colors.YELLOW}Example Complex Queries:{Colors.END}
  "Search for Bitcoin news and analyze wallet 0x742d35Cc... with strategic recommendations"
  "Find Ethereum price predictions, get NFTs for wallet X, and provide investment advice"
  "Research DeFi protocols, analyze my portfolio, and suggest optimization strategies"
  "Get latest crypto news, check wallet balances, and use sequential thinking for market analysis"

{Colors.YELLOW}Enhanced Features:{Colors.END}
  • Multi-step reasoning with visible planning process
  • Automatic tool selection and sequencing
  • Information synthesis from multiple sources
  • Strategic analysis using sequential thinking
  • Detailed execution metadata and confidence scoring
"""
    print(help_text)

async def main():
    """Main enhanced CLI function."""
    parser = argparse.ArgumentParser(
        description="Enhanced LangGraph MCP Agent CLI with Multi-Step Reasoning",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python enhanced_cli.py                                    # Interactive mode
  python enhanced_cli.py --query "Complex multi-step query"  # Single query
  python enhanced_cli.py --tools                           # List tools
        """
    )
    
    parser.add_argument(
        "--query", "-q",
        type=str,
        help="Run a single complex query and exit"
    )
    
    parser.add_argument(
        "--tools", "-t",
        action="store_true",
        help="List available tools and exit"
    )
    
    parser.add_argument(
        "--no-banner",
        action="store_true",
        help="Don't show the banner"
    )
    
    args = parser.parse_args()
    
    # Show banner unless disabled
    if not args.no_banner:
        print_banner()
    
    # Check environment
    if not os.getenv("OPENROUTER_API_KEY"):
        print(f"{Colors.RED}❌ Error: OPENROUTER_API_KEY not found in environment variables{Colors.END}")
        print(f"{Colors.YELLOW}Please make sure your .env file is configured correctly.{Colors.END}")
        return 1
    
    # Initialize enhanced agent
    print(f"{Colors.CYAN}🚀 Initializing Enhanced LangGraph MCP Agent...{Colors.END}")
    
    agent = None
    try:
        agent = await create_enhanced_agent()
        print(f"{Colors.GREEN}✅ Enhanced agent initialized successfully{Colors.END}")
        
        # Handle different modes
        if args.tools:
            print_tools(agent)
            return 0
        
        elif args.query:
            success = await single_query_mode(agent, args.query)
            return 0 if success else 1
        
        else:
            # Default to interactive mode
            await interactive_mode(agent)
            return 0
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}👋 Interrupted by user{Colors.END}")
        return 0
    except Exception as e:
        print(f"{Colors.RED}❌ Error initializing enhanced agent: {e}{Colors.END}")
        return 1
    finally:
        if agent:
            await agent.cleanup()

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}👋 Goodbye!{Colors.END}")
        sys.exit(0)
