# 🚀 Quick Start Guide

## 1. Install Dependencies
```bash
pip install -r requirements.txt
```

## 2. Start Chatting
```bash
python cli.py
```

## 3. Try These Commands

**Math & Basic Operations:**
```
What's 25 * 4 + 100?
Calculate 15% of 200
Convert "hello world" to uppercase and hash it
```

**Advanced Reasoning:**
```
Use sequential thinking to analyze the best investment strategy for crypto
Think step by step about how to evaluate an NFT collection
```

**Web Search & Research (Dual Engines):**
```
Search for recent news about Bitcoin price predictions for 2025
Find information about the latest DeFi protocols using both search engines
Extract content from https://example.com/crypto-article
Search for Ethereum ETF news with <PERSON><PERSON> and DuckDuckGo
```

**Blockchain & NFT Analysis:**
```
Get NFTs for wallet ******************************************
What's the net worth of wallet ******************************************?
Get token balances for ******************************************
```

**Solana Support:**
```
Get Solana portfolio for DJvE3EuZHwEzp2FwjKjLVkCXUj5srjqiDM
Get Solana token price for So11111111111111111111111111111111111111112
```

**JSON & Crypto Address Analysis:**
```
Validate this JSON: {"name": "test", "value": 123}
Analyze this address: ******************************************
What type of address is ******************************************?
```

**Utility:**
```
tools          # List all 114 available tools
help           # Show help
quit           # Exit
```

## 4. That's It!

Your agent is ready to help with **114 tools** across 5 specialized servers:

**🔧 Utility Tools (14):**
- ✅ Math calculations & percentages
- ✅ Text processing & hashing (SHA256, MD5, SHA1)
- ✅ JSON validation & formatting
- ✅ Base64 encoding/decoding
- ✅ Crypto address analysis

**🧠 Sequential Thinking (1):**
- ✅ Advanced reasoning for complex problems
- ✅ Step-by-step analysis and planning

**🔍 Dual Web Search (6):**
- ✅ Tavily: Real-time web search with intelligent results
- ✅ DuckDuckGo: Privacy-focused backup search engine
- ✅ Content extraction from web pages
- ✅ Web crawling and mapping capabilities
- ✅ Comprehensive research and data collection

**🌐 Moralis Blockchain API (93):**
- ✅ NFT data & metadata (Ethereum & Solana)
- ✅ Token balances & prices
- ✅ Wallet net worth calculation
- ✅ Transaction history & analytics
- ✅ DeFi positions & protocols
- ✅ Cross-chain blockchain queries

The conversation is persistent - the agent remembers your chat history within the session.

**Prerequisites:**
- **Node.js** is required for Sequential Thinking, Tavily, and Moralis servers
- **uv** (`pip install uv`) is required for DuckDuckGo search server
- If external servers aren't available, the agent gracefully falls back to local tools only

---

**Need more features?** Check out the full [README.md](README.md) for advanced usage and customization options.
