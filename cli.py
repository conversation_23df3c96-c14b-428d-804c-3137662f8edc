#!/usr/bin/env python3
"""
AP3X Crypto Agent CLI
Simple terminal interface for the advanced crypto agent.
"""

import asyncio
import argparse
import sys
import os
from typing import Optional
from datetime import datetime
from dotenv import load_dotenv

from agent import create_agent
from langchain_core.runnables import RunnableConfig

# Load environment variables
load_dotenv()

class Colors:
    """ANSI color codes for terminal output."""
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_banner():
    """Print the CLI banner."""
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                    AP3X Crypto Agent                        ║
║        Advanced Multi-Step Reasoning + Self-Healing         ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
"""
    print(banner)

def print_health_status(health_data):
    """Print comprehensive health status."""
    print(f"\n{Colors.PURPLE}🏥 System Health Status:{Colors.END}")
    
    overall_status = health_data.get("overall_status", "unknown")
    status_color = Colors.GREEN if overall_status == "healthy" else Colors.YELLOW
    print(f"  {status_color}Overall Status: {overall_status.upper()}{Colors.END}")
    
    print(f"  {Colors.BLUE}Components: {health_data.get('healthy_components', 0)}/{health_data.get('total_components', 0)} healthy{Colors.END}")
    print(f"  {Colors.YELLOW}Recent Failures: {health_data.get('recent_failures', 0)}{Colors.END}")
    
    # Circuit breaker status
    circuit_breakers = health_data.get("circuit_breakers", {})
    if circuit_breakers:
        print(f"  {Colors.CYAN}Circuit Breakers:{Colors.END}")
        for name, state in circuit_breakers.items():
            state_color = Colors.GREEN if state == "CLOSED" else Colors.RED
            print(f"    {state_color}{name}: {state}{Colors.END}")
    
    # Component details
    components = health_data.get("components", {})
    if components:
        print(f"  {Colors.CYAN}Component Details:{Colors.END}")
        for name, details in components.items():
            status = details.get("status", "unknown")
            response_time = details.get("response_time", 0)
            error_rate = details.get("error_rate", 0) * 100
            
            status_color = Colors.GREEN if status == "healthy" else Colors.RED
            print(f"    {status_color}{name}: {status} ({response_time:.2f}s, {error_rate:.1f}% errors){Colors.END}")

def print_recovery_events(events):
    """Print recent recovery events."""
    if not events:
        return
        
    print(f"\n{Colors.PURPLE}🔧 Recent Recovery Events:{Colors.END}")
    for event in events[-5:]:  # Show last 5 events
        timestamp = event.get("timestamp", "")
        component = event.get("component", "")
        failure_type = event.get("failure_type", "")
        recovery_time = event.get("recovery_time", 0)
        success = event.get("success", False)
        
        success_color = Colors.GREEN if success else Colors.RED
        success_text = "SUCCESS" if success else "FAILED"
        
        print(f"  {success_color}[{timestamp[:19]}] {component} - {failure_type} - {success_text} ({recovery_time:.2f}s){Colors.END}")

async def interactive_mode(agent):
    """Run the agent in interactive chat mode."""
    print(f"\n{Colors.GREEN}💬 Interactive Mode{Colors.END}")
    print(f"{Colors.CYAN}Ask complex questions! The agent has 114 tools and advanced reasoning capabilities.{Colors.END}")
    print(f"{Colors.YELLOW}Commands: 'health' for status, 'recover <component>' to trigger recovery, 'help' for assistance, 'quit' to exit{Colors.END}")
    
    config = RunnableConfig(recursion_limit=50, thread_id="cli_session")
    
    while True:
        try:
            # Get user input
            user_input = input(f"\n{Colors.BLUE}You:{Colors.END} ").strip()
            
            if not user_input:
                continue
                
            # Handle special commands
            if user_input.lower() in ['quit', 'exit', 'q']:
                print(f"{Colors.YELLOW}👋 Goodbye!{Colors.END}")
                break
            elif user_input.lower() in ['help', 'h']:
                print_help()
                continue
            elif user_input.lower() == 'health':
                health_status = agent.get_health_status()
                print_health_status(health_status)
                recovery_events = agent.get_recent_recovery_events()
                print_recovery_events(recovery_events)
                continue
            elif user_input.lower().startswith('recover '):
                component = user_input[8:].strip()
                print(f"{Colors.YELLOW}Triggering recovery for {component}...{Colors.END}")
                success = await agent.trigger_recovery(component)
                if success:
                    print(f"{Colors.GREEN}✅ Recovery successful for {component}{Colors.END}")
                else:
                    print(f"{Colors.RED}❌ Recovery failed for {component}{Colors.END}")
                continue
            elif user_input.lower() == 'check':
                print(f"{Colors.YELLOW}Forcing health check...{Colors.END}")
                health_status = await agent.force_health_check()
                print_health_status(health_status)
                continue
            elif user_input.lower() in ['clear', 'cls']:
                os.system('cls' if os.name == 'nt' else 'clear')
                print_banner()
                continue
            elif user_input.lower() == 'tools':
                print_tools(agent)
                continue
            
            # Process the query
            print(f"{Colors.PURPLE}Agent:{Colors.END} Processing your request...")
            
            try:
                result = await agent.invoke(user_input, config)
                
                # Display the response
                final_response = result.get("final_response", "No response generated")
                print(f"\n{Colors.GREEN}Response:{Colors.END} {final_response}")
                
                # Display healing metadata if available
                healing_metadata = result.get("healing_metadata", {})
                if healing_metadata:
                    execution_time = healing_metadata.get("execution_time", 0)
                    print(f"\n{Colors.CYAN}🔧 Execution Metadata:{Colors.END}")
                    print(f"  Execution Time: {execution_time:.2f}s")
                    
                    # Show health status if there were issues
                    health_status = healing_metadata.get("health_status", {})
                    if health_status.get("overall_status") != "healthy":
                        print(f"  {Colors.YELLOW}Health Status: {health_status.get('overall_status', 'unknown')}{Colors.END}")
                    
                    # Show recovery events if any occurred
                    recovery_events = healing_metadata.get("recovery_events", [])
                    if recovery_events:
                        print(f"  {Colors.YELLOW}Recovery Events: {len(recovery_events)} during execution{Colors.END}")
                
            except Exception as e:
                print(f"{Colors.RED}❌ Error: {e}{Colors.END}")
                print(f"{Colors.YELLOW}💡 The self-healing system is working to resolve this issue.{Colors.END}")
                
        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}👋 Interrupted by user{Colors.END}")
            break
        except Exception as e:
            print(f"{Colors.RED}❌ Unexpected error: {e}{Colors.END}")

async def single_query_mode(agent, query: str) -> bool:
    """Process a single query and return success status."""
    try:
        print(f"{Colors.PURPLE}Processing:{Colors.END} {query}")
        
        config = RunnableConfig(recursion_limit=50, thread_id="single_query")
        result = await agent.invoke(query, config)
        
        # Display the response
        final_response = result.get("final_response", "No response generated")
        print(f"\n{Colors.GREEN}Response:{Colors.END} {final_response}")
        
        # Display healing metadata
        healing_metadata = result.get("healing_metadata", {})
        if healing_metadata:
            execution_time = healing_metadata.get("execution_time", 0)
            health_status = healing_metadata.get("health_status", {})
            recovery_events = healing_metadata.get("recovery_events", [])
            
            print(f"\n{Colors.CYAN}🔧 System Report:{Colors.END}")
            print(f"  Execution Time: {execution_time:.2f}s")
            print(f"  System Health: {health_status.get('overall_status', 'unknown')}")
            print(f"  Recovery Events: {len(recovery_events)}")
            
            if recovery_events:
                print(f"  {Colors.YELLOW}Recovery actions were taken during execution{Colors.END}")
        
        return not result.get("error", False)
        
    except Exception as e:
        print(f"{Colors.RED}❌ Error: {e}{Colors.END}")
        return False

def print_help():
    """Print detailed help information."""
    help_text = f"""
{Colors.CYAN}{Colors.BOLD}AP3X Crypto Agent - Help{Colors.END}

{Colors.YELLOW}🔧 System Commands:{Colors.END}
  {Colors.GREEN}health{Colors.END}                 - Show comprehensive system health status
  {Colors.GREEN}check{Colors.END}                  - Force health check of all components
  {Colors.GREEN}recover <component>{Colors.END}    - Manually trigger recovery for a component
  {Colors.GREEN}tools{Colors.END}                  - List available tools
  
{Colors.YELLOW}💬 Chat Commands:{Colors.END}
  {Colors.GREEN}help{Colors.END}                   - Show this help message
  {Colors.GREEN}clear{Colors.END}                  - Clear screen and show banner
  {Colors.GREEN}quit{Colors.END}                   - Exit the application

{Colors.YELLOW}🎯 Example Queries:{Colors.END}
  • "Search for Bitcoin news and analyze wallet 0x742d35Cc..."
  • "Get NFTs for wallet abc123 and provide investment advice"
  • "What's the current price of Ethereum and should I buy?"
  • "Analyze my DeFi positions and suggest optimizations"

{Colors.YELLOW}🏥 Self-Healing Features:{Colors.END}
  • Automatic failure detection and recovery
  • Circuit breakers for external services
  • Adaptive fallback systems
  • Real-time health monitoring
  • Performance optimization

{Colors.CYAN}The agent automatically handles failures and recovers without user intervention.{Colors.END}
"""
    print(help_text)

def print_tools(agent):
    """Print available tools."""
    tools = agent.list_tools()
    print(f"\n{Colors.CYAN}🛠️  Available Tools ({len(tools)} total):{Colors.END}")
    
    # Group tools by category
    categories = {
        "Utility": [t for t in tools if any(name in t for name in ["add", "multiply", "text", "hash"])],
        "Search": [t for t in tools if any(name in t for name in ["search", "tavily", "duckduckgo"])],
        "Blockchain": [t for t in tools if any(name in t for name in ["evm_", "solana_", "moralis"])],
        "Thinking": [t for t in tools if "thinking" in t]
    }
    
    for category, category_tools in categories.items():
        if category_tools:
            print(f"  {Colors.GREEN}{category} ({len(category_tools)} tools){Colors.END}")
            for tool in category_tools[:3]:
                print(f"    • {tool}")
            if len(category_tools) > 3:
                print(f"    • ... and {len(category_tools) - 3} more")

async def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description="AP3X Crypto Agent CLI")
    parser.add_argument("--query", "-q", help="Single query mode")
    parser.add_argument("--tools", "-t", action="store_true", help="List available tools")
    parser.add_argument("--health", action="store_true", help="Show health status and exit")
    
    args = parser.parse_args()
    
    # Print banner
    print_banner()
    
    # Initialize agent
    print(f"{Colors.CYAN}🚀 Initializing AP3X Crypto Agent...{Colors.END}")
    
    agent = None
    try:
        agent = await create_agent()
        print(f"{Colors.GREEN}✅ Agent initialized successfully{Colors.END}")
        
        # Handle different modes
        if args.health:
            health_status = agent.get_health_status()
            print_health_status(health_status)
            recovery_events = agent.get_recent_recovery_events()
            print_recovery_events(recovery_events)
            return 0
        
        elif args.tools:
            print_tools(agent)
            return 0
        
        elif args.query:
            success = await single_query_mode(agent, args.query)
            return 0 if success else 1
        
        else:
            # Default to interactive mode
            await interactive_mode(agent)
            return 0
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}👋 Interrupted by user{Colors.END}")
        return 0
    except Exception as e:
        print(f"{Colors.RED}❌ Error initializing agent: {e}{Colors.END}")
        return 1
    finally:
        if agent:
            await agent.cleanup()

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
