#!/usr/bin/env python3
"""
LangGraph Agent with MCP Integration
This module creates a LangGraph agent that can use MCP (Model Context Protocol) tools.
"""

import os
import asyncio
from typing import Dict, Any, List
from dotenv import load_dotenv

from langchain_openai import Chat<PERSON>penAI
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.runnables import RunnableConfig
from mcp_config import get_safe_mcp_configs, print_server_info, clean_config_for_mcp

# Load environment variables
load_dotenv()

class LangGraphMCPAgent:
    """LangGraph agent with MCP capabilities."""
    
    def __init__(self):
        self.model = None
        self.client = None
        self.agent = None
        self.tools = []
        
    def _setup_model(self):
        """Setup the language model using OpenRouter."""
        api_key = os.getenv("OPENROUTER_API_KEY")
        model_name = os.getenv("OPENROUTER_MODEL", "moonshotai/kimi-k2")
        
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY not found in environment variables")
        
        # Configure OpenAI client to use OpenRouter
        self.model = ChatOpenAI(
            model=model_name,
            openai_api_key=api_key,
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=4000
        )
        
        print(f"✅ Model configured: {model_name}")
    
    def _setup_mcp_client(self, server_configs: Dict[str, Dict[str, Any]]):
        """Setup MCP client with server configurations."""
        self.client = MultiServerMCPClient(server_configs)
        print(f"✅ MCP client configured with {len(server_configs)} server(s)")
    
    async def initialize(self, server_configs: Dict[str, Dict[str, Any]] = None):
        """Initialize the agent with model and MCP tools."""

        # Setup model
        self._setup_model()

        # Default server configuration if none provided
        if server_configs is None:
            server_configs = get_safe_mcp_configs()
            print_server_info(server_configs)

        # Clean configuration for MCP client (remove display fields)
        clean_configs = clean_config_for_mcp(server_configs)

        # Setup MCP client
        self._setup_mcp_client(clean_configs)

        # Get tools from MCP servers using the new API with error handling
        try:
            self.tools = await self.client.get_tools()
            print(f"✅ Loaded {len(self.tools)} tools from MCP servers")

            # Print tool summary by server
            tool_names = [tool.name for tool in self.tools]
            print(f"🔧 Available tools: {', '.join(tool_names[:10])}" +
                  (f" and {len(tool_names)-10} more..." if len(tool_names) > 10 else ""))

        except Exception as e:
            print(f"⚠️  Warning: Some MCP servers may not be available: {e}")
            print("🔄 Retrying with minimal configuration...")

            # Fallback to minimal configuration
            from mcp_config import MINIMAL_CONFIG
            clean_minimal = clean_config_for_mcp(MINIMAL_CONFIG)
            self._setup_mcp_client(clean_minimal)
            self.tools = await self.client.get_tools()
            print(f"✅ Loaded {len(self.tools)} tools from local server only")

        # Create the LangGraph agent
        prompt = """You are a helpful AI assistant with access to various tools through MCP (Model Context Protocol).

You have access to multiple specialized tool servers:

🔧 **Utility Tools:**
- Mathematical calculations (add, multiply, divide, percentages)
- Text processing (uppercase, lowercase, length, hashing)
- Encoding/decoding (Base64)
- JSON validation and formatting
- Cryptocurrency address analysis

🧠 **Sequential Thinking:**
- Advanced reasoning and problem-solving
- Step-by-step analysis for complex problems
- Structured thinking processes

🔍 **Web Search (Tavily + DuckDuckGo):**
- **Tavily (primary)**: Real-time web search with intelligent results
- **DuckDuckGo (backup)**: Privacy-focused web search and content fetching
- Intelligent data extraction from web pages
- News and research article retrieval
- Content parsing and summarization

🌐 **Moralis (if available):**
- Blockchain data and analytics
- NFT information and metadata
- Token prices and market data
- Wallet analysis and transactions
- Cross-chain blockchain queries

Always use the appropriate tools when they can help answer the user's question.
Be clear about what tools you're using and why.
For complex problems, consider using the Sequential Thinking tool to break down the problem systematically.
"""

        self.agent = create_react_agent(
            model=self.model,
            tools=self.tools,
            prompt=prompt,
            checkpointer=MemorySaver()
        )

        print("✅ LangGraph agent created successfully")
    
    async def invoke(self, message: str, config: RunnableConfig = None) -> Dict[str, Any]:
        """Invoke the agent with a message."""
        if not self.agent:
            raise RuntimeError("Agent not initialized. Call initialize() first.")
        
        if config is None:
            config = RunnableConfig(
                recursion_limit=30,
                thread_id="default"
            )
        
        response = await self.agent.ainvoke(
            {"messages": [{"role": "user", "content": message}]},
            config=config
        )
        
        return response
    
    async def stream(self, message: str, config: RunnableConfig = None):
        """Stream the agent response."""
        if not self.agent:
            raise RuntimeError("Agent not initialized. Call initialize() first.")
        
        if config is None:
            config = RunnableConfig(
                recursion_limit=30,
                thread_id="default"
            )
        
        async for chunk in self.agent.astream(
            {"messages": [{"role": "user", "content": message}]},
            config=config
        ):
            yield chunk
    
    def list_tools(self) -> List[str]:
        """List available tools."""
        if not self.tools:
            return []
        return [tool.name for tool in self.tools]
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.client:
            # The new API doesn't require explicit cleanup
            print("✅ MCP client cleaned up")

# Utility function for easy agent creation
async def create_agent(server_configs: Dict[str, Dict[str, Any]] = None) -> LangGraphMCPAgent:
    """Create and initialize a LangGraph MCP agent."""
    agent = LangGraphMCPAgent()
    await agent.initialize(server_configs)
    return agent

# Example usage
async def main():
    """Example usage of the LangGraph MCP agent."""
    agent = None
    try:
        # Create and initialize the agent
        agent = await create_agent()
        
        # List available tools
        print("\n📋 Available tools:")
        for tool in agent.list_tools():
            print(f"  - {tool}")
        
        # Example interactions
        examples = [
            "What's 15 * 23 + 47?",
            "Convert 'Hello World' to uppercase and then hash it with SHA256",
            "Validate this JSON: {'name': 'test', 'value': 123}",
            "What type of crypto address is this: ******************************************?"
        ]
        
        print("\n🤖 Running example interactions:")
        for i, example in enumerate(examples, 1):
            print(f"\n--- Example {i} ---")
            print(f"User: {example}")
            
            response = await agent.invoke(example)
            if response and "messages" in response:
                last_message = response["messages"][-1]
                print(f"Agent: {last_message.content}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        if agent:
            await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
